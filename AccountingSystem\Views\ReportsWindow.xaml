<Window x:Class="AccountingSystem.Views.ReportsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="التقارير المالية - نظام المحاسبة المتقدم" 
        Height="650" Width="1000"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#795548">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="📈" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="التقارير المالية" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="📤 تصدير التقرير" 
                            Background="#4CAF50" Foreground="White"
                            Width="140" Height="35" Margin="5,0"
                            Click="ExportReportButton_Click"/>
                    <Button Content="إغلاق" 
                            Background="#F44336" Foreground="White"
                            Width="80" Height="35" Margin="5,0"
                            Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="300"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Reports Menu -->
            <Border Grid.Column="0" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,0,1,0" Padding="15">
                <StackPanel>
                    <TextBlock Text="أنواع التقارير" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                    
                    <!-- Financial Reports -->
                    <TextBlock Text="التقارير المالية" FontSize="14" FontWeight="Bold" Margin="0,0,0,10" Foreground="#795548"/>
                    
                    <Button x:Name="IncomeStatementButton" 
                            Content="💰 قائمة الدخل"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="IncomeStatementButton_Click"/>
                    
                    <Button x:Name="BalanceSheetButton" 
                            Content="⚖️ الميزانية العمومية"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="BalanceSheetButton_Click"/>
                    
                    <Button x:Name="CashFlowButton" 
                            Content="💸 قائمة التدفقات النقدية"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="CashFlowButton_Click"/>
                    
                    <Button x:Name="TrialBalanceButton" 
                            Content="📊 ميزان المراجعة"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="TrialBalanceButton_Click"/>
                    
                    <!-- Detailed Reports -->
                    <TextBlock Text="التقارير التفصيلية" FontSize="14" FontWeight="Bold" Margin="0,20,0,10" Foreground="#795548"/>
                    
                    <Button x:Name="AccountsReportButton" 
                            Content="📋 تقرير الحسابات"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="AccountsReportButton_Click"/>
                    
                    <Button x:Name="JournalReportButton" 
                            Content="📚 تقرير القيود اليومية"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="JournalReportButton_Click"/>
                    
                    <Button x:Name="InvoicesReportButton" 
                            Content="📄 تقرير الفواتير"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="InvoicesReportButton_Click"/>
                    
                    <Button x:Name="CustomersReportButton"
                            Content="👥 تقرير العملاء"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="CustomersReportButton_Click"/>
                    
                    <!-- Date Range Selection -->
                    <TextBlock Text="فترة التقرير" FontSize="14" FontWeight="Bold" Margin="0,20,0,10" Foreground="#795548"/>
                    
                    <StackPanel>
                        <TextBlock Text="من تاريخ:" FontSize="12" Margin="0,0,0,5"/>
                        <TextBox x:Name="FromDateTextBox" Height="30" FontSize="12" Padding="5"
                                 Background="White" Text="01/01/2024"/>
                    </StackPanel>
                    
                    <StackPanel Margin="0,10,0,0">
                        <TextBlock Text="إلى تاريخ:" FontSize="12" Margin="0,0,0,5"/>
                        <TextBox x:Name="ToDateTextBox" Height="30" FontSize="12" Padding="5"
                                 Background="White" Text="31/12/2024"/>
                    </StackPanel>
                    
                    <Button Content="🔄 تحديث التقرير" 
                            Height="35" FontSize="13" Margin="0,15,0,0"
                            Background="#795548" Foreground="White"
                            Click="RefreshReportButton_Click"/>
                </StackPanel>
            </Border>
            
            <!-- Report Content Area -->
            <Grid Grid.Column="1" Background="White" Margin="20,0,0,0">
                
                <!-- Default Welcome Screen -->
                <Grid x:Name="WelcomeContent" Visibility="Visible">
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="📈" FontSize="80" HorizontalAlignment="Center" Margin="0,0,0,20" Foreground="#795548"/>
                        <TextBlock Text="مرحباً بك في قسم التقارير المالية" 
                                   FontSize="24" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="اختر نوع التقرير من القائمة الجانبية لعرضه" 
                                   FontSize="16" HorizontalAlignment="Center" Foreground="#666"/>
                    </StackPanel>
                </Grid>
                
                <!-- Report Display Area -->
                <ScrollViewer x:Name="ReportScrollViewer" Visibility="Collapsed" VerticalScrollBarVisibility="Auto">
                    <StackPanel x:Name="ReportContent" Margin="20">
                        <!-- Report content will be dynamically loaded here -->
                    </StackPanel>
                </ScrollViewer>
                
            </Grid>
        </Grid>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="StatusLabel"
                           Text="جاهز - اختر تقريراً لعرضه"
                           VerticalAlignment="Center" Margin="10,0"
                           FontSize="12" Foreground="#666"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <TextBlock Text="تاريخ آخر تحديث:" VerticalAlignment="Center" Margin="0,0,5,0" FontSize="12"/>
                    <TextBlock x:Name="LastUpdateLabel" Text="--" 
                               VerticalAlignment="Center" FontWeight="Bold" 
                               Foreground="#795548"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
