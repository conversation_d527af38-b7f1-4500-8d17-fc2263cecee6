<Window x:Class="AccountingSystem.Views.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات النظام - نظام المحاسبة المتقدم" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#3F51B5">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="⚙️" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إعدادات النظام" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Settings Categories -->
            <Border Grid.Column="0" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,0,1,0" Padding="15">
                <StackPanel>
                    <TextBlock Text="فئات الإعدادات" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                    
                    <Button x:Name="CompanyInfoButton" 
                            Content="🏢 معلومات الشركة"
                            Height="40" FontSize="13"
                            Background="#E3F2FD" Foreground="#1976D2"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="CompanyInfoButton_Click"/>
                    
                    <Button x:Name="LanguageButton" 
                            Content="🌐 اللغة والأرقام"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="LanguageButton_Click"/>
                    
                    <Button x:Name="CurrencyButton" 
                            Content="💰 العملات"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="CurrencyButton_Click"/>
                    
                    <Button x:Name="ThemeButton" 
                            Content="🎨 الألوان والمظهر"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="ThemeButton_Click"/>
                    
                    <Button x:Name="PrintingButton" 
                            Content="🖨️ إعدادات الطباعة"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="PrintingButton_Click"/>
                    
                    <Button x:Name="BackupButton" 
                            Content="💾 النسخ الاحتياطي"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="BackupButton_Click"/>
                    
                    <Button x:Name="SecurityButton" 
                            Content="🔒 الأمان والخصوصية"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="SecurityButton_Click"/>
                    
                    <Button x:Name="SystemButton" 
                            Content="🖥️ إعدادات النظام"
                            Height="40" FontSize="13"
                            Background="White" Foreground="#333"
                            HorizontalAlignment="Stretch"
                            Margin="0,2"
                            Click="SystemButton_Click"/>
                </StackPanel>
            </Border>
            
            <!-- Settings Content Area -->
            <ScrollViewer Grid.Column="1" VerticalScrollBarVisibility="Auto" Margin="20,0,0,0">
                <StackPanel>
                    <!-- Company Information Panel -->
                <StackPanel x:Name="CompanyInfoPanel" Visibility="Visible">
                    <TextBlock Text="معلومات الشركة" FontSize="20" FontWeight="Bold" Margin="0,0,0,20" Foreground="#3F51B5"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                            <TextBlock Text="اسم الشركة/المؤسسة *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="CompanyNameTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="#F9F9F9" Margin="0,0,0,15"
                                     Text="شركة المحاسبة المتقدمة"/>
                            
                            <TextBlock Text="اسم الشركة بالإنجليزية" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="CompanyNameEnTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="#F9F9F9" Margin="0,0,0,15"
                                     Text="Advanced Accounting Company"/>
                            
                            <TextBlock Text="الرقم الضريبي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="TaxNumberTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="#F9F9F9" Margin="0,0,0,15"
                                     Text="***************"/>
                            
                            <TextBlock Text="السجل التجاري" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="CommercialRegisterTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="#F9F9F9" Margin="0,0,0,15"
                                     Text="**********"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" Margin="15,0,0,0">
                            <TextBlock Text="رقم الهاتف" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="PhoneTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="#F9F9F9" Margin="0,0,0,15"
                                     Text="+966 11 123 4567"/>
                            
                            <TextBlock Text="البريد الإلكتروني" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="EmailTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="#F9F9F9" Margin="0,0,0,15"
                                     Text="<EMAIL>"/>
                            
                            <TextBlock Text="الموقع الإلكتروني" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="WebsiteTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="#F9F9F9" Margin="0,0,0,15"
                                     Text="www.company.com"/>
                            
                            <TextBlock Text="المدينة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="CityComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,15">
                                <ComboBoxItem Content="الرياض" IsSelected="True"/>
                                <ComboBoxItem Content="جدة"/>
                                <ComboBoxItem Content="الدمام"/>
                                <ComboBoxItem Content="مكة"/>
                                <ComboBoxItem Content="المدينة"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>
                    
                    <TextBlock Text="العنوان التفصيلي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="AddressTextBox" 
                             Height="60" FontSize="14" Padding="10"
                             Background="#F9F9F9" Margin="0,0,0,15"
                             TextWrapping="Wrap" AcceptsReturn="True"
                             Text="شارع الملك فهد، حي العليا، الرياض 12345، المملكة العربية السعودية"/>
                    
                    <TextBlock Text="شعار الشركة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <Button Content="📁 اختيار شعار" Width="120" Height="35" Margin="0,0,10,0"
                                Background="#2196F3" Foreground="White"
                                Click="SelectLogoButton_Click"/>
                        <Button Content="🗑️ حذف الشعار" Width="120" Height="35"
                                Background="#F44336" Foreground="White"
                                Click="RemoveLogoButton_Click"/>
                    </StackPanel>
                </StackPanel>
                
                <!-- Language and Numbers Panel -->
                <StackPanel x:Name="LanguagePanel" Visibility="Collapsed">
                    <TextBlock Text="اللغة والأرقام" FontSize="20" FontWeight="Bold" Margin="0,0,0,20" Foreground="#3F51B5"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                            <TextBlock Text="لغة الواجهة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="LanguageComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,20"
                                      SelectionChanged="LanguageComboBox_SelectionChanged">
                                <ComboBoxItem Content="العربية" IsSelected="True"/>
                                <ComboBoxItem Content="English"/>
                                <ComboBoxItem Content="Français"/>
                            </ComboBox>
                            
                            <TextBlock Text="نوع الأرقام" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="NumberTypeComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,20"
                                      SelectionChanged="NumberTypeComboBox_SelectionChanged">
                                <ComboBoxItem Content="أرقام عربية (١٢٣٤٥٦٧٨٩٠)"/>
                                <ComboBoxItem Content="أرقام هندية (1234567890)" IsSelected="True"/>
                            </ComboBox>
                            
                            <TextBlock Text="اتجاه النص" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="TextDirectionComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,20">
                                <ComboBoxItem Content="من اليمين إلى اليسار (RTL)" IsSelected="True"/>
                                <ComboBoxItem Content="من اليسار إلى اليمين (LTR)"/>
                            </ComboBox>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" Margin="15,0,0,0">
                            <TextBlock Text="تنسيق التاريخ" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="DateFormatComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,20">
                                <ComboBoxItem Content="dd/MM/yyyy" IsSelected="True"/>
                                <ComboBoxItem Content="MM/dd/yyyy"/>
                                <ComboBoxItem Content="yyyy-MM-dd"/>
                                <ComboBoxItem Content="dd-MM-yyyy"/>
                            </ComboBox>
                            
                            <TextBlock Text="تنسيق الوقت" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="TimeFormatComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,20">
                                <ComboBoxItem Content="24 ساعة (HH:mm)" IsSelected="True"/>
                                <ComboBoxItem Content="12 ساعة (hh:mm AM/PM)"/>
                            </ComboBox>
                            
                            <TextBlock Text="الخط الافتراضي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="FontFamilyComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,20">
                                <ComboBoxItem Content="Segoe UI" IsSelected="True"/>
                                <ComboBoxItem Content="Arial"/>
                                <ComboBoxItem Content="Tahoma"/>
                                <ComboBoxItem Content="Calibri"/>
                            </ComboBox>
                        </StackPanel>
                    </Grid>
                    
                    <Border Background="#E3F2FD" CornerRadius="5" Padding="15" Margin="0,10,0,0">
                        <StackPanel>
                            <TextBlock Text="معاينة" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock x:Name="PreviewTextBlock" 
                                       Text="التاريخ: 15/01/2025 - الوقت: 14:30 - المبلغ: 1,234.56 ر.س"
                                       FontSize="13" Background="White" Padding="10"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
                
                <!-- Currency Panel -->
                <StackPanel x:Name="CurrencyPanel" Visibility="Collapsed">
                    <TextBlock Text="إعدادات العملات" FontSize="20" FontWeight="Bold" Margin="0,0,0,20" Foreground="#3F51B5"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="200"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                            <TextBlock Text="العملة الأساسية" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="BaseCurrencyComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,15">
                                <ComboBoxItem Content="ريال سعودي (SAR)" IsSelected="True"/>
                                <ComboBoxItem Content="دولار أمريكي (USD)"/>
                                <ComboBoxItem Content="يورو (EUR)"/>
                                <ComboBoxItem Content="جنيه إسترليني (GBP)"/>
                                <ComboBoxItem Content="درهم إماراتي (AED)"/>
                            </ComboBox>
                            
                            <TextBlock Text="رمز العملة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="CurrencySymbolTextBox"
                                     Height="35" FontSize="14" Padding="10"
                                     Background="#F9F9F9" Margin="0,0,0,15"
                                     Text="ر.س"
                                     TextChanged="CurrencySymbolTextBox_TextChanged"/>
                            
                            <TextBlock Text="موضع رمز العملة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="CurrencyPositionComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,15"
                                      SelectionChanged="CurrencyPositionComboBox_SelectionChanged">
                                <ComboBoxItem Content="بعد المبلغ (1,234.56 ر.س)" IsSelected="True"/>
                                <ComboBoxItem Content="قبل المبلغ (ر.س 1,234.56)"/>
                            </ComboBox>
                            
                            <TextBlock Text="عدد الخانات العشرية" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="DecimalPlacesComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,15">
                                <ComboBoxItem Content="0"/>
                                <ComboBoxItem Content="1"/>
                                <ComboBoxItem Content="2" IsSelected="True"/>
                                <ComboBoxItem Content="3"/>
                                <ComboBoxItem Content="4"/>
                            </ComboBox>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="العملات المدعومة" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                            <ListBox x:Name="SupportedCurrenciesListBox" Height="200" Background="#F9F9F9">
                                <ListBoxItem Content="ريال سعودي (SAR)" IsSelected="True"/>
                                <ListBoxItem Content="دولار أمريكي (USD)"/>
                                <ListBoxItem Content="يورو (EUR)"/>
                                <ListBoxItem Content="جنيه إسترليني (GBP)"/>
                                <ListBoxItem Content="درهم إماراتي (AED)"/>
                            </ListBox>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                <Button Content="➕" Width="30" Height="30" Margin="0,0,5,0"
                                        Background="#4CAF50" Foreground="White"
                                        Click="AddCurrencyButton_Click"/>
                                <Button Content="➖" Width="30" Height="30" Margin="0,0,5,0"
                                        Background="#F44336" Foreground="White"
                                        Click="RemoveCurrencyButton_Click"/>
                                <Button Content="✏️" Width="30" Height="30"
                                        Background="#FF9800" Foreground="White"
                                        Click="EditCurrencyButton_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </Grid>
                    
                    <Border Background="#E8F5E8" CornerRadius="5" Padding="15" Margin="0,15,0,0">
                        <StackPanel>
                            <TextBlock Text="معاينة تنسيق العملة" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                            <TextBlock x:Name="CurrencyPreviewTextBlock" 
                                       Text="المبلغ: 1,234.56 ر.س"
                                       FontSize="16" Background="White" Padding="10"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- Theme and Colors Panel -->
                <StackPanel x:Name="ThemePanel" Visibility="Collapsed">
                    <TextBlock Text="الألوان والمظهر" FontSize="20" FontWeight="Bold" Margin="0,0,0,20" Foreground="#3F51B5"/>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                            <TextBlock Text="المظهر العام" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="ThemeComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,15">
                                <ComboBoxItem Content="فاتح (Light)" IsSelected="True"/>
                                <ComboBoxItem Content="داكن (Dark)"/>
                                <ComboBoxItem Content="تلقائي (Auto)"/>
                            </ComboBox>

                            <TextBlock Text="اللون الأساسي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <Button x:Name="PrimaryColorButton" Width="100" Height="35" Margin="0,0,10,0"
                                        Background="#2196F3" Content="أزرق"
                                        Click="SelectPrimaryColorButton_Click"/>
                                <Button Content="🎨 اختيار لون" Width="100" Height="35"
                                        Background="#666" Foreground="White"
                                        Click="CustomPrimaryColorButton_Click"/>
                            </StackPanel>

                            <TextBlock Text="اللون الثانوي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <Button x:Name="SecondaryColorButton" Width="100" Height="35" Margin="0,0,10,0"
                                        Background="#4CAF50" Content="أخضر"
                                        Click="SelectSecondaryColorButton_Click"/>
                                <Button Content="🎨 اختيار لون" Width="100" Height="35"
                                        Background="#666" Foreground="White"
                                        Click="CustomSecondaryColorButton_Click"/>
                            </StackPanel>

                            <TextBlock Text="ألوان سريعة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <Button Width="30" Height="30" Background="#2196F3" Margin="2" Click="QuickColor_Click" Tag="#2196F3"/>
                                <Button Width="30" Height="30" Background="#4CAF50" Margin="2" Click="QuickColor_Click" Tag="#4CAF50"/>
                                <Button Width="30" Height="30" Background="#FF9800" Margin="2" Click="QuickColor_Click" Tag="#FF9800"/>
                                <Button Width="30" Height="30" Background="#F44336" Margin="2" Click="QuickColor_Click" Tag="#F44336"/>
                                <Button Width="30" Height="30" Background="#9C27B0" Margin="2" Click="QuickColor_Click" Tag="#9C27B0"/>
                                <Button Width="30" Height="30" Background="#607D8B" Margin="2" Click="QuickColor_Click" Tag="#607D8B"/>
                            </StackPanel>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="15,0,0,0">
                            <TextBlock Text="حجم الخط" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="FontSizeComboBox"
                                      Height="35" FontSize="14" Padding="10"
                                      Background="#F9F9F9" Margin="0,0,0,15">
                                <ComboBoxItem Content="صغير (12px)"/>
                                <ComboBoxItem Content="متوسط (14px)" IsSelected="True"/>
                                <ComboBoxItem Content="كبير (16px)"/>
                                <ComboBoxItem Content="كبير جداً (18px)"/>
                            </ComboBox>

                            <TextBlock Text="شفافية النوافذ" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <Slider x:Name="OpacitySlider" Minimum="0.7" Maximum="1.0" Value="1.0"
                                    Height="30" Margin="0,0,0,5"/>
                            <TextBlock x:Name="OpacityLabel" Text="100%" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                            <TextBlock Text="تأثيرات بصرية" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                            <CheckBox Content="تأثيرات الانتقال" IsChecked="True" Margin="0,5"/>
                            <CheckBox Content="ظلال النوافذ" IsChecked="True" Margin="0,5"/>
                            <CheckBox Content="تأثيرات الأزرار" IsChecked="True" Margin="0,5"/>
                            <CheckBox Content="الرسوم المتحركة" IsChecked="False" Margin="0,5"/>
                        </StackPanel>
                    </Grid>

                    <Border Background="#F0F0F0" CornerRadius="5" Padding="15" Margin="0,15,0,0">
                        <StackPanel>
                            <TextBlock Text="معاينة المظهر" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                            <Border x:Name="ThemePreviewBorder" Background="White" BorderBrush="#DDD" BorderThickness="1" CornerRadius="5" Padding="15">
                                <StackPanel>
                                    <TextBlock Text="عنوان تجريبي" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                                    <Button Content="زر تجريبي" Width="100" Height="30" Margin="0,5"/>
                                    <TextBlock Text="نص تجريبي للمعاينة" FontSize="12" Margin="0,5"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- Other Panels (Printing, Backup, Security, System) -->
                <StackPanel x:Name="PrintingPanel" Visibility="Collapsed">
                    <TextBlock Text="إعدادات الطباعة" FontSize="20" FontWeight="Bold" Margin="0,0,0,20" Foreground="#3F51B5"/>
                    <TextBlock Text="سيتم تطوير هذا القسم قريباً..." FontSize="14" HorizontalAlignment="Center" Margin="0,50"/>
                </StackPanel>

                <StackPanel x:Name="BackupPanel" Visibility="Collapsed">
                    <TextBlock Text="النسخ الاحتياطي" FontSize="20" FontWeight="Bold" Margin="0,0,0,20" Foreground="#3F51B5"/>
                    <TextBlock Text="سيتم تطوير هذا القسم قريباً..." FontSize="14" HorizontalAlignment="Center" Margin="0,50"/>
                </StackPanel>

                <StackPanel x:Name="SecurityPanel" Visibility="Collapsed">
                    <TextBlock Text="الأمان والخصوصية" FontSize="20" FontWeight="Bold" Margin="0,0,0,20" Foreground="#3F51B5"/>
                    <TextBlock Text="سيتم تطوير هذا القسم قريباً..." FontSize="14" HorizontalAlignment="Center" Margin="0,50"/>
                </StackPanel>

                <StackPanel x:Name="SystemPanel" Visibility="Collapsed">
                    <TextBlock Text="إعدادات النظام" FontSize="20" FontWeight="Bold" Margin="0,0,0,20" Foreground="#3F51B5"/>
                    <TextBlock Text="سيتم تطوير هذا القسم قريباً..." FontSize="14" HorizontalAlignment="Center" Margin="0,50"/>
                </StackPanel>

                </StackPanel>
            </ScrollViewer>
        </Grid>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ الإعدادات" 
                        Width="140" Height="40" FontSize="14" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White" Margin="10,0"
                        Click="SaveSettingsButton_Click"/>
                <Button Content="🔄 استعادة الافتراضي" 
                        Width="140" Height="40" FontSize="14"
                        Background="#FF9800" Foreground="White" Margin="10,0"
                        Click="RestoreDefaultsButton_Click"/>
                <Button Content="📤 تصدير الإعدادات" 
                        Width="140" Height="40" FontSize="14"
                        Background="#2196F3" Foreground="White" Margin="10,0"
                        Click="ExportSettingsButton_Click"/>
                <Button Content="❌ إغلاق" 
                        Width="140" Height="40" FontSize="14"
                        Background="#F44336" Foreground="White" Margin="10,0"
                        Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
