<Window x:Class="AccountingSystem.Views.BasicSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات النظام - نظام المحاسبة" 
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanMinimize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3">
            <TextBlock Text="⚙️ إعدادات النظام" 
                       FontSize="18" FontWeight="Bold" 
                       Foreground="White" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"/>
        </Border>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="20">
            <StackPanel>
                
                <!-- Company Info -->
                <GroupBox Header="🏢 معلومات الشركة" FontWeight="Bold" Margin="0,0,0,20">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="35"/>
                            <RowDefinition Height="35"/>
                            <RowDefinition Height="35"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الشركة:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="1" x:Name="CompanyNameTextBox" 
                                 Text="شركة المحاسبة المتقدمة" Margin="5,0,0,0"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الرقم الضريبي:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="1" Grid.Column="1" x:Name="TaxNumberTextBox" 
                                 Text="300123456789003" Margin="5,0,0,0"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="رقم الهاتف:" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="2" Grid.Column="1" x:Name="PhoneTextBox" 
                                 Text="+966 11 123 4567" Margin="5,0,0,0"/>
                    </Grid>
                </GroupBox>
                
                <!-- Format Settings -->
                <GroupBox Header="🌍 إعدادات التنسيق (من نظام ويندوز)" FontWeight="Bold" Margin="0,0,0,20">
                    <TextBox x:Name="FormatInfoTextBox" 
                             IsReadOnly="True" 
                             TextWrapping="Wrap" 
                             Height="120" 
                             Background="#F0F8FF"
                             FontFamily="Consolas"
                             Margin="10"/>
                </GroupBox>
                
                <!-- System Settings -->
                <GroupBox Header="🔧 إعدادات النظام" FontWeight="Bold">
                    <Grid Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="30"/>
                            <RowDefinition Height="30"/>
                            <RowDefinition Height="30"/>
                            <RowDefinition Height="30"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="200"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="النسخ الاحتياطي التلقائي:" VerticalAlignment="Center"/>
                        <CheckBox Grid.Row="0" Grid.Column="1" IsChecked="True" Content="مفعل" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="الإشعارات:" VerticalAlignment="Center"/>
                        <CheckBox Grid.Row="1" Grid.Column="1" IsChecked="True" Content="مفعلة" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="حفظ سجل العمليات:" VerticalAlignment="Center"/>
                        <CheckBox Grid.Row="2" Grid.Column="1" IsChecked="True" Content="مفعل" VerticalAlignment="Center"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="التحديث التلقائي:" VerticalAlignment="Center"/>
                        <CheckBox Grid.Row="3" Grid.Column="1" IsChecked="True" Content="مفعل" VerticalAlignment="Center"/>
                    </Grid>
                </GroupBox>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
                <Button x:Name="SaveButton" Content="💾 حفظ الإعدادات" 
                        Width="120" Height="35" Margin="5"
                        Background="#28A745" Foreground="White" 
                        Click="SaveButton_Click"/>
                <Button x:Name="WindowsSettingsButton" Content="🔧 إعدادات ويندوز" 
                        Width="130" Height="35" Margin="5"
                        Background="#17A2B8" Foreground="White" 
                        Click="WindowsSettingsButton_Click"/>
                <Button x:Name="CloseButton" Content="❌ إغلاق" 
                        Width="100" Height="35" Margin="5"
                        Background="#6C757D" Foreground="White" 
                        Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
        
    </Grid>
</Window>
