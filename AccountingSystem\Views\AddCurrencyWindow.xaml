<Window x:Class="AccountingSystem.Views.AddCurrencyWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة عملة جديدة - نظام المحاسبة المتقدم" 
        Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#FF9800">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="💰" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إضافة عملة جديدة" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <StackPanel Grid.Row="1" Margin="30,20">
            
            <TextBlock Text="معلومات العملة" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#FF9800"/>
            
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="اسم العملة *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="CurrencyNameTextBox" 
                             Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="اسم العملة بالإنجليزية *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="CurrencyNameEnTextBox" 
                             Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9"/>
                </StackPanel>
            </Grid>
            
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="رمز العملة *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="CurrencySymbolTextBox" 
                             Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="الكود الدولي (ISO)" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="CurrencyCodeTextBox" 
                             Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" MaxLength="3"/>
                </StackPanel>
            </Grid>
            
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="سعر الصرف مقابل العملة الأساسية" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="ExchangeRateTextBox" 
                             Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" Text="1.00"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="عدد الخانات العشرية" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="DecimalPlacesComboBox"
                              Height="35" FontSize="14" Padding="10"
                              Background="#F9F9F9">
                        <ComboBoxItem Content="0"/>
                        <ComboBoxItem Content="1"/>
                        <ComboBoxItem Content="2" IsSelected="True"/>
                        <ComboBoxItem Content="3"/>
                        <ComboBoxItem Content="4"/>
                    </ComboBox>
                </StackPanel>
            </Grid>
            
            <TextBlock Text="موضع رمز العملة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
            <ComboBox x:Name="SymbolPositionComboBox"
                      Height="35" FontSize="14" Padding="10"
                      Background="#F9F9F9" Margin="0,0,0,20">
                <ComboBoxItem Content="بعد المبلغ (1,234.56 $)" IsSelected="True"/>
                <ComboBoxItem Content="قبل المبلغ ($ 1,234.56)"/>
            </ComboBox>
            
            <TextBlock Text="فاصل الآلاف" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
            <ComboBox x:Name="ThousandsSeparatorComboBox"
                      Height="35" FontSize="14" Padding="10"
                      Background="#F9F9F9" Margin="0,0,0,20">
                <ComboBoxItem Content="فاصلة (,)" IsSelected="True"/>
                <ComboBoxItem Content="نقطة (.)"/>
                <ComboBoxItem Content="مسافة ( )"/>
                <ComboBoxItem Content="بدون فاصل"/>
            </ComboBox>
            
            <TextBlock Text="فاصل العشرية" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
            <ComboBox x:Name="DecimalSeparatorComboBox"
                      Height="35" FontSize="14" Padding="10"
                      Background="#F9F9F9" Margin="0,0,0,20">
                <ComboBoxItem Content="نقطة (.)" IsSelected="True"/>
                <ComboBoxItem Content="فاصلة (,)"/>
            </ComboBox>
            
            <CheckBox x:Name="IsActiveCheckBox" 
                      Content="العملة نشطة" 
                      FontSize="14" 
                      IsChecked="True"
                      Margin="0,10,0,0"/>
            
            <Border Background="#FFF3E0" CornerRadius="5" Padding="15" Margin="0,15,0,0">
                <StackPanel>
                    <TextBlock Text="معاينة تنسيق العملة" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                    <TextBlock x:Name="CurrencyPreviewTextBlock" 
                               Text="المبلغ: 1,234.56 $"
                               FontSize="16" Background="White" Padding="10"/>
                </StackPanel>
            </Border>
            
        </StackPanel>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ العملة" 
                        Width="120" Height="40" FontSize="14" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White" Margin="10,0"
                        Click="SaveCurrencyButton_Click"/>
                <Button Content="👁️ معاينة" 
                        Width="120" Height="40" FontSize="14"
                        Background="#2196F3" Foreground="White" Margin="10,0"
                        Click="PreviewButton_Click"/>
                <Button Content="🔄 مسح الحقول" 
                        Width="120" Height="40" FontSize="14"
                        Background="#FF9800" Foreground="White" Margin="10,0"
                        Click="ClearButton_Click"/>
                <Button Content="❌ إلغاء" 
                        Width="120" Height="40" FontSize="14"
                        Background="#F44336" Foreground="White" Margin="10,0"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
