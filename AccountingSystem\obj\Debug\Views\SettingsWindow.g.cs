﻿#pragma checksum "..\..\..\Views\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "CA4DB9C58E80631F3725E87A62877067E22DA081"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem.Views {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 42 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CompanyInfoButton;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button LanguageButton;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CurrencyButton;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ThemeButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrintingButton;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BackupButton;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SecurityButton;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SystemButton;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CompanyInfoPanel;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyNameEnTextBox;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TaxNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CommercialRegisterTextBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WebsiteTextBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CityComboBox;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LanguagePanel;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox NumberTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TextDirectionComboBox;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DateFormatComboBox;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TimeFormatComboBox;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontFamilyComboBox;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewTextBlock;
        
        #line default
        #line hidden
        
        
        #line 278 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CurrencyPanel;
        
        #line default
        #line hidden
        
        
        #line 289 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BaseCurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 300 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrencySymbolTextBox;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyPositionComboBox;
        
        #line default
        #line hidden
        
        
        #line 316 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DecimalPlacesComboBox;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SupportedCurrenciesListBox;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrencyPreviewTextBlock;
        
        #line default
        #line hidden
        
        
        #line 362 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ThemePanel;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PrimaryColorButton;
        
        #line default
        #line hidden
        
        
        #line 393 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SecondaryColorButton;
        
        #line default
        #line hidden
        
        
        #line 414 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 424 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider OpacitySlider;
        
        #line default
        #line hidden
        
        
        #line 426 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OpacityLabel;
        
        #line default
        #line hidden
        
        
        #line 439 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ThemePreviewBorder;
        
        #line default
        #line hidden
        
        
        #line 451 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PrintingPanel;
        
        #line default
        #line hidden
        
        
        #line 456 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel BackupPanel;
        
        #line default
        #line hidden
        
        
        #line 461 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SecurityPanel;
        
        #line default
        #line hidden
        
        
        #line 466 "..\..\..\Views\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SystemPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem;component/views/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CompanyInfoButton = ((System.Windows.Controls.Button)(target));
            
            #line 48 "..\..\..\Views\SettingsWindow.xaml"
            this.CompanyInfoButton.Click += new System.Windows.RoutedEventHandler(this.CompanyInfoButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.LanguageButton = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\Views\SettingsWindow.xaml"
            this.LanguageButton.Click += new System.Windows.RoutedEventHandler(this.LanguageButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CurrencyButton = ((System.Windows.Controls.Button)(target));
            
            #line 64 "..\..\..\Views\SettingsWindow.xaml"
            this.CurrencyButton.Click += new System.Windows.RoutedEventHandler(this.CurrencyButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ThemeButton = ((System.Windows.Controls.Button)(target));
            
            #line 72 "..\..\..\Views\SettingsWindow.xaml"
            this.ThemeButton.Click += new System.Windows.RoutedEventHandler(this.ThemeButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PrintingButton = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\Views\SettingsWindow.xaml"
            this.PrintingButton.Click += new System.Windows.RoutedEventHandler(this.PrintingButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BackupButton = ((System.Windows.Controls.Button)(target));
            
            #line 88 "..\..\..\Views\SettingsWindow.xaml"
            this.BackupButton.Click += new System.Windows.RoutedEventHandler(this.BackupButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SecurityButton = ((System.Windows.Controls.Button)(target));
            
            #line 96 "..\..\..\Views\SettingsWindow.xaml"
            this.SecurityButton.Click += new System.Windows.RoutedEventHandler(this.SecurityButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SystemButton = ((System.Windows.Controls.Button)(target));
            
            #line 104 "..\..\..\Views\SettingsWindow.xaml"
            this.SystemButton.Click += new System.Windows.RoutedEventHandler(this.SystemButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CompanyInfoPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.CompanyNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.CompanyNameEnTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.TaxNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.CommercialRegisterTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.PhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.EmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.WebsiteTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.CityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.AddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            
            #line 190 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectLogoButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 193 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveLogoButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.LanguagePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 22:
            this.LanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 212 "..\..\..\Views\SettingsWindow.xaml"
            this.LanguageComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LanguageComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 23:
            this.NumberTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 222 "..\..\..\Views\SettingsWindow.xaml"
            this.NumberTypeComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.NumberTypeComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 24:
            this.TextDirectionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 25:
            this.DateFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 26:
            this.TimeFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 27:
            this.FontFamilyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 28:
            this.PreviewTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.CurrencyPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 30:
            this.BaseCurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 31:
            this.CurrencySymbolTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 304 "..\..\..\Views\SettingsWindow.xaml"
            this.CurrencySymbolTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CurrencySymbolTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 32:
            this.CurrencyPositionComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 310 "..\..\..\Views\SettingsWindow.xaml"
            this.CurrencyPositionComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CurrencyPositionComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 33:
            this.DecimalPlacesComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 34:
            this.SupportedCurrenciesListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 35:
            
            #line 340 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddCurrencyButton_Click);
            
            #line default
            #line hidden
            return;
            case 36:
            
            #line 343 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveCurrencyButton_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            
            #line 346 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EditCurrencyButton_Click);
            
            #line default
            #line hidden
            return;
            case 38:
            this.CurrencyPreviewTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 39:
            this.ThemePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 40:
            this.ThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 41:
            this.PrimaryColorButton = ((System.Windows.Controls.Button)(target));
            
            #line 385 "..\..\..\Views\SettingsWindow.xaml"
            this.PrimaryColorButton.Click += new System.Windows.RoutedEventHandler(this.SelectPrimaryColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 42:
            
            #line 388 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CustomPrimaryColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.SecondaryColorButton = ((System.Windows.Controls.Button)(target));
            
            #line 395 "..\..\..\Views\SettingsWindow.xaml"
            this.SecondaryColorButton.Click += new System.Windows.RoutedEventHandler(this.SelectSecondaryColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            
            #line 398 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CustomSecondaryColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            
            #line 403 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            
            #line 404 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            
            #line 405 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            
            #line 406 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 49:
            
            #line 407 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 50:
            
            #line 408 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QuickColor_Click);
            
            #line default
            #line hidden
            return;
            case 51:
            this.FontSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 52:
            this.OpacitySlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 53:
            this.OpacityLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            this.ThemePreviewBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 55:
            this.PrintingPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 56:
            this.BackupPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 57:
            this.SecurityPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 58:
            this.SystemPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 59:
            
            #line 481 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 60:
            
            #line 485 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RestoreDefaultsButton_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            
            #line 489 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 62:
            
            #line 493 "..\..\..\Views\SettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

