using System;
using System.Globalization;

namespace AccountingSystem.Helpers
{
    /// <summary>
    /// مساعد لتنسيق القيم حسب إعدادات نظام ويندوز
    /// </summary>
    public static class SystemFormatHelper
    {
        /// <summary>
        /// تنسيق التاريخ حسب إعدادات النظام
        /// </summary>
        public static string FormatDate(DateTime date)
        {
            return date.ToString("d", CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// تنسيق التاريخ والوقت حسب إعدادات النظام
        /// </summary>
        public static string FormatDateTime(DateTime dateTime)
        {
            return dateTime.ToString("g", CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// تنسيق الوقت حسب إعدادات النظام
        /// </summary>
        public static string FormatTime(DateTime time)
        {
            return time.ToString("t", CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// تنسيق المبلغ كعملة حسب إعدادات النظام
        /// </summary>
        public static string FormatCurrency(decimal amount)
        {
            return amount.ToString("C", CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// تنسيق الرقم حسب إعدادات النظام
        /// </summary>
        public static string FormatNumber(decimal number, int decimalPlaces = 2)
        {
            return number.ToString("N" + decimalPlaces, CultureInfo.CurrentCulture);
        }

        /// <summary>
        /// تنسيق النسبة المئوية حسب إعدادات النظام
        /// </summary>
        public static string FormatPercentage(decimal percentage)
        {
            return percentage.ToString("N2", CultureInfo.CurrentCulture) + "%";
        }

        /// <summary>
        /// الحصول على رمز العملة من إعدادات النظام
        /// </summary>
        public static string GetCurrencySymbol()
        {
            return CultureInfo.CurrentCulture.NumberFormat.CurrencySymbol;
        }

        /// <summary>
        /// الحصول على فاصل الآلاف من إعدادات النظام
        /// </summary>
        public static string GetNumberGroupSeparator()
        {
            return CultureInfo.CurrentCulture.NumberFormat.NumberGroupSeparator;
        }

        /// <summary>
        /// الحصول على الفاصلة العشرية من إعدادات النظام
        /// </summary>
        public static string GetDecimalSeparator()
        {
            return CultureInfo.CurrentCulture.NumberFormat.NumberDecimalSeparator;
        }

        /// <summary>
        /// تحويل النص إلى رقم حسب إعدادات النظام
        /// </summary>
        public static bool TryParseNumber(string text, out decimal result)
        {
            return decimal.TryParse(text, NumberStyles.Number, CultureInfo.CurrentCulture, out result);
        }

        /// <summary>
        /// تحويل النص إلى مبلغ حسب إعدادات النظام
        /// </summary>
        public static bool TryParseCurrency(string text, out decimal result)
        {
            return decimal.TryParse(text, NumberStyles.Currency, CultureInfo.CurrentCulture, out result);
        }

        /// <summary>
        /// تحويل النص إلى تاريخ حسب إعدادات النظام
        /// </summary>
        public static bool TryParseDate(string text, out DateTime result)
        {
            return DateTime.TryParse(text, CultureInfo.CurrentCulture, DateTimeStyles.None, out result);
        }

        /// <summary>
        /// الحصول على معلومات المنطقة الحالية
        /// </summary>
        public static string GetCurrentCultureInfo()
        {
            var culture = CultureInfo.CurrentCulture;
            return string.Format("المنطقة: {0}\nاللغة: {1}\nالبلد: {2}",
                culture.DisplayName,
                culture.TwoLetterISOLanguageName.ToUpper(),
                culture.Name);
        }

        /// <summary>
        /// الحصول على نموذج للتنسيقات المختلفة
        /// </summary>
        public static string GetFormatSamples()
        {
            var now = DateTime.Now;
            var sampleAmount = 1234.56m;
            
            return string.Format(
                "نماذج التنسيق:\n" +
                "التاريخ: {0}\n" +
                "الوقت: {1}\n" +
                "العملة: {2}\n" +
                "الأرقام: {3}\n" +
                "النسبة: {4}",
                FormatDate(now),
                FormatTime(now),
                FormatCurrency(sampleAmount),
                FormatNumber(sampleAmount),
                FormatPercentage(15.5m)
            );
        }
    }
}
