<Application x:Class="AccountingSystem.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:helpers="clr-namespace:AccountingSystem.Helpers">

    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design Theme -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Button.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Card.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.TextBox.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.PasswordBox.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.CheckBox.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.ProgressBar.xaml" />

                <!-- Custom Styles -->
                <ResourceDictionary>
                    <!-- Converters -->
                    <helpers:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
                    <helpers:BoolToBackgroundConverter x:Key="BoolToBackgroundConverter"/>
                    <helpers:EnumToDisplayConverter x:Key="EnumToDisplayConverter"/>
                    <helpers:BalanceColorConverter x:Key="BalanceColorConverter"/>
                    <helpers:FreezeIconConverter x:Key="FreezeIconConverter"/>
                    <helpers:FreezeTooltipConverter x:Key="FreezeTooltipConverter"/>
                    <helpers:DateToShortStringConverter x:Key="DateToShortStringConverter"/>
                    <helpers:CurrencyConverter x:Key="CurrencyConverter"/>
                    <helpers:PercentageConverter x:Key="PercentageConverter"/>
                    <helpers:StatusToColorConverter x:Key="StatusToColorConverter"/>
                    <helpers:EmptyStringToDefaultConverter x:Key="EmptyStringToDefaultConverter"/>
                    <helpers:DraftStatusToVisibilityConverter x:Key="DraftStatusToVisibilityConverter"/>
                    <helpers:PostedStatusToVisibilityConverter x:Key="PostedStatusToVisibilityConverter"/>
                    <helpers:BalanceToColorConverter x:Key="BalanceToColorConverter"/>
                    <helpers:JournalEntryStatusToColorConverter x:Key="JournalEntryStatusToColorConverter"/>
                    <helpers:NullToVisibilityConverter x:Key="NullToVisibilityConverter"/>
                    <helpers:EmptyStringToVisibilityConverter x:Key="EmptyStringToVisibilityConverter"/>
                    <helpers:InvoiceStatusToColorConverter x:Key="InvoiceStatusToColorConverter"/>
                    <helpers:PositiveAmountToVisibilityConverter x:Key="PositiveAmountToVisibilityConverter"/>
                    <helpers:BoolToAdminConverter x:Key="BoolToAdminConverter"/>
                    <helpers:BoolToStatusColorConverter x:Key="BoolToStatusColorConverter"/>
                    <helpers:BoolToStatusTextConverter x:Key="BoolToStatusTextConverter"/>
                    <helpers:BoolToToggleIconConverter x:Key="BoolToToggleIconConverter"/>
                    <helpers:BoolToToggleColorConverter x:Key="BoolToToggleColorConverter"/>
                    <helpers:BoolToToggleTooltipConverter x:Key="BoolToToggleTooltipConverter"/>
                    <helpers:AdminUserToVisibilityConverter x:Key="AdminUserToVisibilityConverter"/>

                    <!-- Main Window Style -->
                    <Style x:Key="MainWindowStyle" TargetType="Window">
                        <Setter Property="WindowStyle" Value="None"/>
                        <Setter Property="AllowsTransparency" Value="True"/>
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="ResizeMode" Value="CanResize"/>
                    </Style>
                    
                    <!-- Card Style -->
                    <Style x:Key="CardStyle" TargetType="materialDesign:Card">
                        <Setter Property="Margin" Value="10"/>
                        <Setter Property="Padding" Value="15"/>
                        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
                    </Style>
                    
                    <!-- Button Style -->
                    <Style x:Key="MenuButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                        <Setter Property="Height" Value="50"/>
                        <Setter Property="HorizontalAlignment" Value="Stretch"/>
                        <Setter Property="HorizontalContentAlignment" Value="Left"/>
                        <Setter Property="Padding" Value="20,0"/>
                        <Setter Property="Margin" Value="5,2"/>
                        <Setter Property="FontSize" Value="14"/>
                        <Setter Property="FontFamily" Value="Segoe UI"/>
                    </Style>
                    
                    <!-- Title Style -->
                    <Style x:Key="TitleStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="24"/>
                        <Setter Property="FontWeight" Value="Bold"/>
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                        <Setter Property="Margin" Value="0,0,0,20"/>
                    </Style>
                    
                    <!-- Subtitle Style -->
                    <Style x:Key="SubtitleStyle" TargetType="TextBlock">
                        <Setter Property="FontSize" Value="16"/>
                        <Setter Property="FontWeight" Value="Medium"/>
                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                        <Setter Property="Margin" Value="0,0,0,10"/>
                    </Style>
                </ResourceDictionary>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Application.Resources>
</Application>
