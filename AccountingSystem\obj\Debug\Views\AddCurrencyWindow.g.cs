﻿#pragma checksum "..\..\..\Views\AddCurrencyWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1FD0BD71EF6950D58469FFAD972F902105EAA88D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem.Views {
    
    
    /// <summary>
    /// AddCurrencyWindow
    /// </summary>
    public partial class AddCurrencyWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 43 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrencyNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrencyNameEnTextBox;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrencySymbolTextBox;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrencyCodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExchangeRateTextBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DecimalPlacesComboBox;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox SymbolPositionComboBox;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThousandsSeparatorComboBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DecimalSeparatorComboBox;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\Views\AddCurrencyWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrencyPreviewTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem;component/views/addcurrencywindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\AddCurrencyWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CurrencyNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.CurrencyNameEnTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CurrencySymbolTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.CurrencyCodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.ExchangeRateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.DecimalPlacesComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.SymbolPositionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.ThousandsSeparatorComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.DecimalSeparatorComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.CurrencyPreviewTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            
            #line 153 "..\..\..\Views\AddCurrencyWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveCurrencyButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 157 "..\..\..\Views\AddCurrencyWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 161 "..\..\..\Views\AddCurrencyWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 165 "..\..\..\Views\AddCurrencyWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

