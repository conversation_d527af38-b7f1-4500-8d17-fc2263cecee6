﻿#pragma checksum "..\..\..\Views\SimpleSettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1F05E4DFC92B642E24CEB3AC743A54DBF768099A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem.Views {
    
    
    /// <summary>
    /// SimpleSettingsWindow
    /// </summary>
    public partial class SimpleSettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CompanyNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TaxNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LanguageComboBox;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox NumberTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DateFormatComboBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TimeFormatComboBox;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox BaseCurrencyComboBox;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CurrencySymbolTextBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CurrencyPositionComboBox;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DecimalPlacesComboBox;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox FontSizeComboBox;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\Views\SimpleSettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PreviewTextBlock;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem;component/views/simplesettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\SimpleSettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CompanyNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.TaxNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.PhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.EmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.LanguageComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.NumberTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.DateFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.TimeFormatComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.BaseCurrencyComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.CurrencySymbolTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.CurrencyPositionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 12:
            this.DecimalPlacesComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 13:
            this.ThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 14:
            this.FontSizeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            
            #line 200 "..\..\..\Views\SimpleSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 201 "..\..\..\Views\SimpleSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 202 "..\..\..\Views\SimpleSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 203 "..\..\..\Views\SimpleSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 204 "..\..\..\Views\SimpleSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ColorButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.PreviewTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            
            #line 232 "..\..\..\Views\SimpleSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveSettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 236 "..\..\..\Views\SimpleSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RestoreDefaultsButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 240 "..\..\..\Views\SimpleSettingsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

