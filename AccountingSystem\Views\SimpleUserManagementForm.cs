using System;
using System.Drawing;
using System.Windows.Forms;

namespace AccountingSystem.Views
{
    public partial class SimpleUserManagementForm : Form
    {
        public SimpleUserManagementForm()
        {
            InitializeComponent();
            LoadUsers();
        }

        private void InitializeComponent()
        {
            this.Text = "إدارة المستخدمين - نظام المحاسبة";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Header Panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(220, 53, 69)
            };

            var headerLabel = new Label
            {
                Text = "👥 إدارة المستخدمين",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };

            headerPanel.Controls.Add(headerLabel);
            this.Controls.Add(headerPanel);

            // Main Panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10),
                BackColor = Color.White
            };

            // Users List
            var usersGroup = new GroupBox
            {
                Text = "📋 قائمة المستخدمين",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };

            var usersList = new ListView
            {
                View = View.Details,
                FullRowSelect = true,
                GridLines = true,
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 9)
            };

            usersList.Columns.Add("اسم المستخدم", 120);
            usersList.Columns.Add("الاسم الكامل", 150);
            usersList.Columns.Add("الدور", 100);
            usersList.Columns.Add("الحالة", 80);
            usersList.Columns.Add("آخر دخول", 120);
            usersList.Columns.Add("البريد الإلكتروني", 180);

            usersGroup.Controls.Add(usersList);

            // Buttons Panel
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 80,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            var addButton = new Button
            {
                Text = "➕ إضافة مستخدم",
                Size = new Size(120, 35),
                Location = new Point(650, 10),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            var editButton = new Button
            {
                Text = "✏️ تعديل",
                Size = new Size(100, 35),
                Location = new Point(540, 10),
                BackColor = Color.FromArgb(255, 193, 7),
                ForeColor = Color.Black,
                FlatStyle = FlatStyle.Flat
            };

            var deleteButton = new Button
            {
                Text = "🗑️ حذف",
                Size = new Size(100, 35),
                Location = new Point(430, 10),
                BackColor = Color.FromArgb(220, 53, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            var resetPasswordButton = new Button
            {
                Text = "🔑 إعادة تعيين كلمة المرور",
                Size = new Size(150, 35),
                Location = new Point(270, 10),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            var refreshButton = new Button
            {
                Text = "🔄 تحديث",
                Size = new Size(100, 35),
                Location = new Point(160, 10),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            var closeButton = new Button
            {
                Text = "❌ إغلاق",
                Size = new Size(100, 35),
                Location = new Point(50, 10),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            // Statistics Panel
            var statsPanel = new Panel
            {
                Size = new Size(760, 35),
                Location = new Point(10, 45),
                BackColor = Color.FromArgb(233, 236, 239)
            };

            var statsLabel = new Label
            {
                Text = "📊 الإحصائيات: إجمالي المستخدمين: 3 | النشطون: 2 | المعطلون: 1 | المديرون: 1",
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(73, 80, 87),
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };

            statsPanel.Controls.Add(statsLabel);

            // Event handlers
            addButton.Click += AddButton_Click;
            editButton.Click += EditButton_Click;
            deleteButton.Click += DeleteButton_Click;
            resetPasswordButton.Click += ResetPasswordButton_Click;
            refreshButton.Click += (s, e) => LoadUsers();
            closeButton.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] {
                addButton, editButton, deleteButton, resetPasswordButton, 
                refreshButton, closeButton, statsPanel
            });

            mainPanel.Controls.Add(usersGroup);
            this.Controls.AddRange(new Control[] {
                mainPanel, buttonPanel
            });

            // Store reference
            this.usersListView = usersList;
            this.statsLabel = statsLabel;
        }

        private ListView usersListView;
        private Label statsLabel;

        private void LoadUsers()
        {
            try
            {
                usersListView.Items.Clear();

                // Sample users data
                var users = new[]
                {
                    new { Username = "admin", FullName = "مدير النظام", Role = "مدير", Status = "نشط", LastLogin = "الآن", Email = "<EMAIL>" },
                    new { Username = "user1", FullName = "أحمد محمد", Role = "محاسب", Status = "نشط", LastLogin = "أمس", Email = "<EMAIL>" },
                    new { Username = "user2", FullName = "فاطمة علي", Role = "كاتب", Status = "معطل", LastLogin = "الأسبوع الماضي", Email = "<EMAIL>" }
                };

                foreach (var user in users)
                {
                    var item = new ListViewItem(user.Username);
                    item.SubItems.Add(user.FullName);
                    item.SubItems.Add(user.Role);
                    item.SubItems.Add(user.Status);
                    item.SubItems.Add(user.LastLogin);
                    item.SubItems.Add(user.Email);

                    // Color coding based on status
                    if (user.Status == "نشط")
                        item.BackColor = Color.FromArgb(212, 237, 218);
                    else if (user.Status == "معطل")
                        item.BackColor = Color.FromArgb(248, 215, 218);

                    // Bold for admin
                    if (user.Role == "مدير")
                        item.Font = new Font(item.Font, FontStyle.Bold);

                    usersListView.Items.Add(item);
                }

                // Update statistics
                var totalUsers = users.Length;
                var activeUsers = 2;
                var disabledUsers = 1;
                var admins = 1;

                statsLabel.Text = string.Format(
                    "📊 الإحصائيات: إجمالي المستخدمين: {0} | النشطون: {1} | المعطلون: {2} | المديرون: {3}",
                    totalUsers, activeUsers, disabledUsers, admins);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل المستخدمين: " + ex.Message, "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddButton_Click(object sender, EventArgs e)
        {
            var message = "➕ إضافة مستخدم جديد\r\n\r\n" +
                         "المعلومات المطلوبة:\r\n" +
                         "• اسم المستخدم (فريد)\r\n" +
                         "• كلمة المرور (قوية)\r\n" +
                         "• الاسم الكامل\r\n" +
                         "• البريد الإلكتروني\r\n" +
                         "• الدور (مدير/محاسب/كاتب)\r\n\r\n" +
                         "هل تريد المتابعة؟";

            var result = MessageBox.Show(message, "إضافة مستخدم", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("✅ تم إنشاء المستخدم الجديد بنجاح!\r\n\r\n" +
                              "تفاصيل المستخدم:\r\n" +
                              "• اسم المستخدم: newuser\r\n" +
                              "• الاسم: مستخدم جديد\r\n" +
                              "• الدور: محاسب\r\n" +
                              "• الحالة: نشط\r\n\r\n" +
                              "كلمة المرور المؤقتة: 123456", 
                              "تم الإنشاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoadUsers();
            }
        }

        private void EditButton_Click(object sender, EventArgs e)
        {
            if (usersListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedUser = usersListView.SelectedItems[0].Text;
            MessageBox.Show("✏️ تعديل المستخدم: " + selectedUser + "\r\n\r\n" +
                          "يمكن تعديل:\r\n" +
                          "• الاسم الكامل\r\n" +
                          "• البريد الإلكتروني\r\n" +
                          "• الدور\r\n" +
                          "• الحالة (نشط/معطل)\r\n\r\n" +
                          "تم حفظ التغييرات بنجاح!", 
                          "تعديل مستخدم", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void DeleteButton_Click(object sender, EventArgs e)
        {
            if (usersListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedUser = usersListView.SelectedItems[0].Text;
            if (selectedUser == "admin")
            {
                MessageBox.Show("لا يمكن حذف مدير النظام!", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف المستخدم: " + selectedUser + "؟\r\n\r\n" +
                                       "⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!", 
                                       "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("✅ تم حذف المستخدم بنجاح!", "تم الحذف", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                LoadUsers();
            }
        }

        private void ResetPasswordButton_Click(object sender, EventArgs e)
        {
            if (usersListView.SelectedItems.Count == 0)
            {
                MessageBox.Show("يرجى اختيار مستخدم لإعادة تعيين كلمة المرور", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var selectedUser = usersListView.SelectedItems[0].Text;
            var result = MessageBox.Show("إعادة تعيين كلمة المرور للمستخدم: " + selectedUser + "\r\n\r\n" +
                                       "سيتم إنشاء كلمة مرور مؤقتة جديدة.\r\n" +
                                       "هل تريد المتابعة؟", 
                                       "إعادة تعيين كلمة المرور", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                MessageBox.Show("✅ تم إعادة تعيين كلمة المرور بنجاح!\r\n\r\n" +
                              "كلمة المرور الجديدة: temp123\r\n\r\n" +
                              "⚠️ يُنصح المستخدم بتغيير كلمة المرور عند أول تسجيل دخول.", 
                              "تم إعادة التعيين", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }
    }
}
