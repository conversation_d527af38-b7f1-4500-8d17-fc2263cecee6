using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class ItemsWindow : Window
    {
        private List<ItemDetail> items;
        private bool isBarcodeMode = false;

        public ItemsWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            items = new List<ItemDetail>
            {
                new ItemDetail { Barcode = "*************", ItemName = "لابتوب ديل XPS 13", Category = "إلكترونيات", Unit = "قطعة", PurchasePrice = "3,500.00", SalePrice = "4,200.00", AvailableQuantity = "15", MinimumStock = "5", Status = "متوفر" },
                new ItemDetail { Barcode = "*************", ItemName = "قميص قطني أزرق", Category = "ملابس", Unit = "قطعة", PurchasePrice = "45.00", SalePrice = "75.00", AvailableQuantity = "50", MinimumStock = "10", Status = "متوفر" },
                new ItemDetail { Barcode = "*************", ItemName = "أرز بسمتي 5 كيلو", Category = "أغذية", Unit = "كيس", PurchasePrice = "25.00", SalePrice = "35.00", AvailableQuantity = "100", MinimumStock = "20", Status = "متوفر" },
                new ItemDetail { Barcode = "4567890123456", ItemName = "قلم حبر جاف أزرق", Category = "مكتبية", Unit = "قطعة", PurchasePrice = "2.50", SalePrice = "5.00", AvailableQuantity = "200", MinimumStock = "50", Status = "متوفر" },
                new ItemDetail { Barcode = "5678901234567", ItemName = "مكنسة كهربائية", Category = "أدوات منزلية", Unit = "قطعة", PurchasePrice = "180.00", SalePrice = "250.00", AvailableQuantity = "8", MinimumStock = "3", Status = "متوفر" },
                new ItemDetail { Barcode = "6789012345678", ItemName = "هاتف سامسونج جالاكسي", Category = "إلكترونيات", Unit = "قطعة", PurchasePrice = "800.00", SalePrice = "1,100.00", AvailableQuantity = "25", MinimumStock = "5", Status = "متوفر" },
                new ItemDetail { Barcode = "7890123456789", ItemName = "فستان صيفي", Category = "ملابس", Unit = "قطعة", PurchasePrice = "80.00", SalePrice = "150.00", AvailableQuantity = "2", MinimumStock = "5", Status = "نفد المخزون" },
                new ItemDetail { Barcode = "8901234567890", ItemName = "زيت زيتون 500 مل", Category = "أغذية", Unit = "زجاجة", PurchasePrice = "15.00", SalePrice = "25.00", AvailableQuantity = "75", MinimumStock = "15", Status = "متوفر" },
                new ItemDetail { Barcode = "9012345678901", ItemName = "دفتر ملاحظات A4", Category = "مكتبية", Unit = "قطعة", PurchasePrice = "8.00", SalePrice = "15.00", AvailableQuantity = "0", MinimumStock = "10", Status = "نفد المخزون" },
                new ItemDetail { Barcode = "0123456789012", ItemName = "مصباح LED", Category = "أدوات منزلية", Unit = "قطعة", PurchasePrice = "25.00", SalePrice = "45.00", AvailableQuantity = "30", MinimumStock = "8", Status = "متوفر" }
            };

            ItemsDataGrid.ItemsSource = items;
            UpdateStatusAndTotals();
        }

        private void UpdateStatusAndTotals()
        {
            StatusLabel.Text = $"جاهز - {items.Count} صنف";
            
            decimal totalValue = 0;
            int outOfStockCount = 0;
            
            foreach (var item in items)
            {
                if (decimal.TryParse(item.PurchasePrice.Replace(",", ""), out decimal price) && 
                    int.TryParse(item.AvailableQuantity, out int qty))
                {
                    totalValue += price * qty;
                }
                
                if (item.Status == "نفد المخزون" || int.Parse(item.AvailableQuantity) == 0)
                {
                    outOfStockCount++;
                }
            }
            
            TotalInventoryValueLabel.Text = $"{totalValue:N2} ر.س";
            OutOfStockLabel.Text = outOfStockCount.ToString();
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox.Text == "البحث في الأصناف أو مسح الباركود...")
            {
                textBox.Text = "";
                textBox.Foreground = Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = "البحث في الأصناف أو مسح الباركود...";
                textBox.Foreground = Brushes.Gray;
            }
        }

        private void SearchTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                SearchItems();
            }
        }

        private void SearchItems()
        {
            var searchText = SearchTextBox.Text.Trim();
            if (string.IsNullOrEmpty(searchText) || searchText == "البحث في الأصناف أو مسح الباركود...")
            {
                ItemsDataGrid.ItemsSource = items;
                return;
            }

            var filteredItems = items.Where(i => 
                i.ItemName.Contains(searchText) || 
                i.Barcode.Contains(searchText) ||
                i.Category.Contains(searchText)).ToList();
            
            ItemsDataGrid.ItemsSource = filteredItems;
            
            if (searchText.Length >= 10 && searchText.All(char.IsDigit))
            {
                // This looks like a barcode scan
                var foundItem = items.FirstOrDefault(i => i.Barcode == searchText);
                if (foundItem != null)
                {
                    MessageBox.Show($"تم العثور على الصنف:\n\nالاسم: {foundItem.ItemName}\nالباركود: {foundItem.Barcode}\nالكمية المتاحة: {foundItem.AvailableQuantity}\nسعر البيع: {foundItem.SalePrice} ر.س", 
                                   "نتيجة مسح الباركود", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("لم يتم العثور على صنف بهذا الباركود", "باركود غير موجود", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
        }

        private void StartBarcodeScanner_Click(object sender, RoutedEventArgs e)
        {
            isBarcodeMode = !isBarcodeMode;
            var button = sender as Button;
            
            if (isBarcodeMode)
            {
                button.Content = "⏹️ إيقاف الماسح";
                button.Background = Brushes.Red;
                SearchTextBox.Focus();
                MessageBox.Show("تم تشغيل وضع مسح الباركود\nامسح الباركود أو اكتبه في حقل البحث", "وضع الباركود", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                button.Content = "📷 تشغيل الماسح";
                button.Background = new SolidColorBrush(Color.FromRgb(33, 150, 243));
                MessageBox.Show("تم إيقاف وضع مسح الباركود", "وضع الباركود", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            SearchItems();
        }

        private void AddItemButton_Click(object sender, RoutedEventArgs e)
        {
            var addItemWindow = new AddItemWindow();
            if (addItemWindow.ShowDialog() == true)
            {
                LoadSampleData();
            }
        }

        private void ViewItemButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as ItemDetail;
            if (item != null)
            {
                MessageBox.Show($"تفاصيل الصنف:\n\nالاسم: {item.ItemName}\nالباركود: {item.Barcode}\nالفئة: {item.Category}\nسعر الشراء: {item.PurchasePrice} ر.س\nسعر البيع: {item.SalePrice} ر.س\nالكمية المتاحة: {item.AvailableQuantity}\nالحد الأدنى: {item.MinimumStock}\nالحالة: {item.Status}", 
                               "تفاصيل الصنف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void EditItemButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as ItemDetail;
            if (item != null)
            {
                MessageBox.Show($"تعديل الصنف: {item.ItemName}", "تعديل صنف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteItemButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as ItemDetail;
            if (item != null)
            {
                var result = MessageBox.Show($"هل تريد حذف الصنف: {item.ItemName}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                if (result == MessageBoxResult.Yes)
                {
                    items.Remove(item);
                    ItemsDataGrid.Items.Refresh();
                    UpdateStatusAndTotals();
                }
            }
        }

        private void StockAdjustmentButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as ItemDetail;
            if (item != null)
            {
                MessageBox.Show($"تعديل مخزون الصنف: {item.ItemName}\nالكمية الحالية: {item.AvailableQuantity}", "تعديل المخزون", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PrintBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as ItemDetail;
            if (item != null)
            {
                MessageBox.Show($"طباعة باركود للصنف: {item.ItemName}\nالباركود: {item.Barcode}", "طباعة باركود", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void InventoryReportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح تقرير المخزون قريباً", "تقرير المخزون", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير البيانات قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم استيراد البيانات قريباً", "استيراد", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class ItemDetail
    {
        public string Barcode { get; set; }
        public string ItemName { get; set; }
        public string Category { get; set; }
        public string Unit { get; set; }
        public string PurchasePrice { get; set; }
        public string SalePrice { get; set; }
        public string AvailableQuantity { get; set; }
        public string MinimumStock { get; set; }
        public string Status { get; set; }
    }
}
