using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class BasicUserManagementWindow : Window
    {
        public class UserInfo
        {
            public string Username { get; set; }
            public string FullName { get; set; }
            public string Role { get; set; }
            public string Status { get; set; }
            public string LastLogin { get; set; }
            public string Email { get; set; }
        }

        public BasicUserManagementWindow()
        {
            InitializeComponent();
            LoadUsers();
        }

        private void LoadUsers()
        {
            try
            {
                var users = new List<UserInfo>
                {
                    new UserInfo 
                    { 
                        Username = "admin", 
                        FullName = "مدير النظام", 
                        Role = "مدير", 
                        Status = "نشط", 
                        LastLogin = "الآن", 
                        Email = "<EMAIL>" 
                    },
                    new UserInfo 
                    { 
                        Username = "user1", 
                        FullName = "أحمد محمد", 
                        Role = "محاسب", 
                        Status = "نشط", 
                        LastLogin = "أمس", 
                        Email = "<EMAIL>" 
                    },
                    new UserInfo 
                    { 
                        Username = "user2", 
                        FullName = "فاطمة علي", 
                        Role = "كاتب", 
                        Status = "معطل", 
                        LastLogin = "الأسبوع الماضي", 
                        Email = "<EMAIL>" 
                    }
                };

                UsersListView.ItemsSource = users;

                // Update statistics
                var totalUsers = users.Count;
                var activeUsers = 2;
                var disabledUsers = 1;
                var admins = 1;

                StatsTextBlock.Text = "📊 الإحصائيات: إجمالي المستخدمين: " + totalUsers + 
                                    " | النشطون: " + activeUsers + 
                                    " | المعطلون: " + disabledUsers + 
                                    " | المديرون: " + admins;
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل المستخدمين: " + ex.Message, "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            var message = "➕ إضافة مستخدم جديد\r\n\r\n" +
                         "المعلومات المطلوبة:\r\n" +
                         "• اسم المستخدم (فريد)\r\n" +
                         "• كلمة المرور (قوية)\r\n" +
                         "• الاسم الكامل\r\n" +
                         "• البريد الإلكتروني\r\n" +
                         "• الدور (مدير/محاسب/كاتب)\r\n\r\n" +
                         "هل تريد المتابعة؟";

            var result = MessageBox.Show(message, "إضافة مستخدم", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("✅ تم إنشاء المستخدم الجديد بنجاح!\r\n\r\n" +
                              "تفاصيل المستخدم:\r\n" +
                              "• اسم المستخدم: newuser\r\n" +
                              "• الاسم: مستخدم جديد\r\n" +
                              "• الدور: محاسب\r\n" +
                              "• الحالة: نشط\r\n\r\n" +
                              "كلمة المرور المؤقتة: 123456", 
                              "تم الإنشاء", MessageBoxButton.OK, MessageBoxImage.Information);
                LoadUsers();
            }
        }

        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            if (UsersListView.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار مستخدم للتعديل", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedUser = (UserInfo)UsersListView.SelectedItem;
            MessageBox.Show("✏️ تعديل المستخدم: " + selectedUser.Username + "\r\n\r\n" +
                          "يمكن تعديل:\r\n" +
                          "• الاسم الكامل\r\n" +
                          "• البريد الإلكتروني\r\n" +
                          "• الدور\r\n" +
                          "• الحالة (نشط/معطل)\r\n\r\n" +
                          "تم حفظ التغييرات بنجاح!", 
                          "تعديل مستخدم", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            if (UsersListView.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار مستخدم للحذف", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedUser = (UserInfo)UsersListView.SelectedItem;
            if (selectedUser.Username == "admin")
            {
                MessageBox.Show("لا يمكن حذف مدير النظام!", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            var result = MessageBox.Show("هل أنت متأكد من حذف المستخدم: " + selectedUser.Username + "؟\r\n\r\n" +
                                       "⚠️ تحذير: هذه العملية لا يمكن التراجع عنها!", 
                                       "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("✅ تم حذف المستخدم بنجاح!", "تم الحذف", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                LoadUsers();
            }
        }

        private void ResetPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            if (UsersListView.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار مستخدم لإعادة تعيين كلمة المرور", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var selectedUser = (UserInfo)UsersListView.SelectedItem;
            var result = MessageBox.Show("إعادة تعيين كلمة المرور للمستخدم: " + selectedUser.Username + "\r\n\r\n" +
                                       "سيتم إنشاء كلمة مرور مؤقتة جديدة.\r\n" +
                                       "هل تريد المتابعة؟", 
                                       "إعادة تعيين كلمة المرور", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("✅ تم إعادة تعيين كلمة المرور بنجاح!\r\n\r\n" +
                              "كلمة المرور الجديدة: temp123\r\n\r\n" +
                              "⚠️ يُنصح المستخدم بتغيير كلمة المرور عند أول تسجيل دخول.", 
                              "تم إعادة التعيين", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadUsers();
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
