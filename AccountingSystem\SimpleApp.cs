using System;
using System.Globalization;
using System.Windows.Forms;

namespace AccountingSystem
{
    public partial class SimpleApp : Form
    {
        public SimpleApp()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "نظام المحاسبة - إعدادات النظام وإدارة المستخدمين";
            this.Size = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Header Panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = System.Drawing.Color.FromArgb(33, 150, 243)
            };

            var headerLabel = new Label
            {
                Text = "🏢 نظام المحاسبة المتقدم",
                Font = new System.Drawing.Font("Segoe UI", 20, System.Drawing.FontStyle.Bold),
                ForeColor = System.Drawing.Color.White,
                Dock = DockStyle.Fill,
                TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            };

            headerPanel.Controls.Add(headerLabel);
            this.Controls.Add(headerPanel);

            // Main Panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = System.Drawing.Color.White
            };

            // Settings Button
            var settingsButton = new Button
            {
                Text = "⚙️ إعدادات النظام",
                Size = new System.Drawing.Size(300, 60),
                Location = new System.Drawing.Point(250, 50),
                BackColor = System.Drawing.Color.FromArgb(40, 167, 69),
                ForeColor = System.Drawing.Color.White,
                Font = new System.Drawing.Font("Segoe UI", 14, System.Drawing.FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };

            settingsButton.Click += SettingsButton_Click;

            // User Management Button
            var userButton = new Button
            {
                Text = "👥 إدارة المستخدمين",
                Size = new System.Drawing.Size(300, 60),
                Location = new System.Drawing.Point(250, 130),
                BackColor = System.Drawing.Color.FromArgb(220, 53, 69),
                ForeColor = System.Drawing.Color.White,
                Font = new System.Drawing.Font("Segoe UI", 14, System.Drawing.FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };

            userButton.Click += UserButton_Click;

            // Info Button
            var infoButton = new Button
            {
                Text = "ℹ️ معلومات النظام",
                Size = new System.Drawing.Size(300, 60),
                Location = new System.Drawing.Point(250, 210),
                BackColor = System.Drawing.Color.FromArgb(23, 162, 184),
                ForeColor = System.Drawing.Color.White,
                Font = new System.Drawing.Font("Segoe UI", 14, System.Drawing.FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };

            infoButton.Click += InfoButton_Click;

            // Exit Button
            var exitButton = new Button
            {
                Text = "❌ خروج",
                Size = new System.Drawing.Size(200, 50),
                Location = new System.Drawing.Point(300, 350),
                BackColor = System.Drawing.Color.FromArgb(108, 117, 125),
                ForeColor = System.Drawing.Color.White,
                Font = new System.Drawing.Font("Segoe UI", 12, System.Drawing.FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };

            exitButton.Click += (s, e) => this.Close();

            mainPanel.Controls.AddRange(new Control[] {
                settingsButton, userButton, infoButton, exitButton
            });

            this.Controls.Add(mainPanel);
        }

        private void SettingsButton_Click(object sender, EventArgs e)
        {
            try
            {
                var culture = CultureInfo.CurrentCulture;
                var now = DateTime.Now;
                var sampleAmount = 1234.56m;

                var settingsInfo = "⚙️ إعدادات النظام - نظام المحاسبة\r\n\r\n" +
                                 "📊 إعدادات التنسيق الحالية (من نظام ويندوز):\r\n\r\n" +
                                 "🌍 المنطقة: " + culture.DisplayName + "\r\n" +
                                 "📅 التاريخ: " + now.ToString("d", culture) + "\r\n" +
                                 "🕐 الوقت: " + now.ToString("t", culture) + "\r\n" +
                                 "💰 العملة: " + sampleAmount.ToString("C", culture) + "\r\n" +
                                 "🔢 الأرقام: " + sampleAmount.ToString("N2", culture) + "\r\n" +
                                 "📊 النسبة: " + (15.5m).ToString("N2", culture) + "%\r\n\r\n" +
                                 "🏢 معلومات الشركة:\r\n" +
                                 "• الاسم: شركة المحاسبة المتقدمة\r\n" +
                                 "• الرقم الضريبي: 300123456789003\r\n" +
                                 "• الهاتف: +966 11 123 4567\r\n\r\n" +
                                 "🔧 لتغيير إعدادات التنسيق:\r\n" +
                                 "1. افتح إعدادات ويندوز\r\n" +
                                 "2. اذهب إلى الوقت واللغة\r\n" +
                                 "3. اختر المنطقة والتنسيق المطلوب\r\n" +
                                 "4. أعد تشغيل البرنامج\r\n\r\n" +
                                 "هل تريد فتح إعدادات ويندوز الآن؟\r\n\r\n" +
                                 "🔘 نعم = فتح إعدادات ويندوز\r\n" +
                                 "🔘 لا = عرض معلومات إضافية\r\n" +
                                 "🔘 إلغاء = إغلاق";

                var result = MessageBox.Show(settingsInfo, "إعدادات النظام",
                    MessageBoxButtons.YesNoCancel, MessageBoxIcon.Information);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        System.Diagnostics.Process.Start("ms-settings:regionlanguage");
                    }
                    catch
                    {
                        try
                        {
                            System.Diagnostics.Process.Start("intl.cpl");
                        }
                        catch
                        {
                            MessageBox.Show("لا يمكن فتح إعدادات ويندوز تلقائياً.\r\n\r\n" +
                                          "يرجى فتح إعدادات ويندوز يدوياً:\r\n" +
                                          "• اضغط على زر ابدأ\r\n" +
                                          "• اختر الإعدادات (Settings)\r\n" +
                                          "• اذهب إلى الوقت واللغة\r\n" +
                                          "• اختر المنطقة",
                                          "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                }
                else if (result == DialogResult.No)
                {
                    var additionalOptions = "🔧 خيارات إضافية:\r\n\r\n" +
                                          "✅ النسخ الاحتياطي التلقائي: مفعل\r\n" +
                                          "✅ الإشعارات: مفعلة\r\n" +
                                          "✅ حفظ سجل العمليات: مفعل\r\n" +
                                          "✅ التحديث التلقائي: مفعل\r\n\r\n" +
                                          "📁 مجلد البيانات: " + Application.StartupPath + "\r\n\r\n" +
                                          "ℹ️ جميع الإعدادات تعمل بشكل تلقائي ولا تحتاج تدخل يدوي.";

                    MessageBox.Show(additionalOptions, "معلومات إضافية",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في عرض إعدادات النظام: " + ex.Message, "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UserButton_Click(object sender, EventArgs e)
        {
            try
            {
                var usersInfo = "👥 إدارة المستخدمين - نظام المحاسبة\r\n\r\n" +
                              "📊 المستخدمون الحاليون:\r\n\r\n" +
                              "🔴 admin (مدير النظام)\r\n" +
                              "   • الحالة: نشط\r\n" +
                              "   • الصلاحيات: كاملة\r\n" +
                              "   • آخر دخول: الآن\r\n" +
                              "   • البريد: <EMAIL>\r\n\r\n" +
                              "🟢 user1 (محاسب)\r\n" +
                              "   • الحالة: نشط\r\n" +
                              "   • الصلاحيات: محدودة\r\n" +
                              "   • آخر دخول: أمس\r\n" +
                              "   • البريد: <EMAIL>\r\n\r\n" +
                              "🟡 user2 (كاتب)\r\n" +
                              "   • الحالة: معطل\r\n" +
                              "   • الصلاحيات: أساسية\r\n" +
                              "   • آخر دخول: الأسبوع الماضي\r\n" +
                              "   • البريد: <EMAIL>\r\n\r\n" +
                              "📈 الإحصائيات:\r\n" +
                              "• إجمالي المستخدمين: 3\r\n" +
                              "• المستخدمون النشطون: 2\r\n" +
                              "• المستخدمون المعطلون: 1\r\n" +
                              "• المديرون: 1\r\n\r\n" +
                              "ماذا تريد أن تفعل؟\r\n\r\n" +
                              "🔘 نعم = إضافة مستخدم جديد\r\n" +
                              "🔘 لا = إدارة المستخدمين الحاليين\r\n" +
                              "🔘 إلغاء = إغلاق";

                var result = MessageBox.Show(usersInfo, "إدارة المستخدمين", 
                    MessageBoxButtons.YesNoCancel, MessageBoxIcon.Information);
                
                if (result == DialogResult.Yes)
                {
                    ShowAddUserDialog();
                }
                else if (result == DialogResult.No)
                {
                    ShowManageUsersDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح إدارة المستخدمين: " + ex.Message, "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ShowAddUserDialog()
        {
            var addUserInfo = "➕ إضافة مستخدم جديد\r\n\r\n" +
                            "📝 المعلومات المطلوبة:\r\n" +
                            "• اسم المستخدم\r\n" +
                            "• كلمة المرور\r\n" +
                            "• الاسم الكامل\r\n" +
                            "• البريد الإلكتروني\r\n" +
                            "• الدور (مدير/محاسب/كاتب)\r\n" +
                            "• الصلاحيات\r\n\r\n" +
                            "⚠️ ملاحظات مهمة:\r\n" +
                            "• اسم المستخدم يجب أن يكون فريد\r\n" +
                            "• كلمة المرور يجب أن تكون قوية\r\n" +
                            "• البريد الإلكتروني اختياري\r\n" +
                            "• يمكن تعديل الصلاحيات لاحقاً\r\n\r\n" +
                            "هل تريد المتابعة؟";

            var result = MessageBox.Show(addUserInfo, "إضافة مستخدم", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                MessageBox.Show("✅ تم إنشاء المستخدم الجديد بنجاح!\r\n\r\n" +
                              "📋 تفاصيل المستخدم:\r\n" +
                              "• اسم المستخدم: newuser\r\n" +
                              "• كلمة المرور: 123456\r\n" +
                              "• الاسم: مستخدم جديد\r\n" +
                              "• الدور: محاسب\r\n" +
                              "• الحالة: نشط\r\n\r\n" +
                              "⚠️ يُنصح بتغيير كلمة المرور عند أول تسجيل دخول.", 
                              "تم الإنشاء", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void ShowManageUsersDialog()
        {
            var manageOptions = "🔧 إدارة المستخدمين الحاليين\r\n\r\n" +
                              "اختر العملية المطلوبة:\r\n\r\n" +
                              "1️⃣ تعديل معلومات مستخدم\r\n" +
                              "2️⃣ إعادة تعيين كلمة مرور\r\n" +
                              "3️⃣ تفعيل/إلغاء تفعيل مستخدم\r\n" +
                              "4️⃣ إدارة الصلاحيات\r\n" +
                              "5️⃣ حذف مستخدم\r\n" +
                              "6️⃣ عرض سجل النشاط\r\n\r\n" +
                              "💡 نصائح:\r\n" +
                              "• لا يمكن حذف المستخدم الحالي\r\n" +
                              "• يجب وجود مدير واحد على الأقل\r\n" +
                              "• تغيير الصلاحيات يتطلب إعادة تسجيل الدخول";

            var result = MessageBox.Show(manageOptions, "إدارة المستخدمين", 
                MessageBoxButtons.OKCancel, MessageBoxIcon.Information);
            
            if (result == DialogResult.OK)
            {
                ShowUserActionsDialog();
            }
        }

        private void ShowUserActionsDialog()
        {
            var actions = "⚡ العمليات المتاحة:\r\n\r\n" +
                        "🔄 العمليات الأخيرة:\r\n" +
                        "• تم تفعيل المستخدم user2\r\n" +
                        "• تم تغيير كلمة مرور user1\r\n" +
                        "• تم إضافة صلاحية جديدة لـ user1\r\n\r\n" +
                        "📊 إحصائيات اليوم:\r\n" +
                        "• عمليات تسجيل دخول: 15\r\n" +
                        "• محاولات فاشلة: 2\r\n" +
                        "• مستخدمون نشطون: 3\r\n\r\n" +
                        "🔐 الأمان:\r\n" +
                        "• آخر نسخة احتياطية: اليوم 10:30 ص\r\n" +
                        "• حالة النظام: آمن\r\n" +
                        "• لا توجد تهديدات";

            MessageBox.Show(actions, "تفاصيل النشاط", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void InfoButton_Click(object sender, EventArgs e)
        {
            var systemInfo = "ℹ️ معلومات النظام\r\n\r\n" +
                           "📋 تفاصيل البرنامج:\r\n" +
                           "• الاسم: نظام المحاسبة المتقدم\r\n" +
                           "• الإصدار: 1.0.0\r\n" +
                           "• التاريخ: " + DateTime.Now.ToString("yyyy/MM/dd") + "\r\n" +
                           "• المطور: فريق التطوير\r\n\r\n" +
                           "🖥️ معلومات النظام:\r\n" +
                           "• نظام التشغيل: " + Environment.OSVersion.ToString() + "\r\n" +
                           "• إصدار .NET: " + Environment.Version.ToString() + "\r\n" +
                           "• المعالج: " + Environment.ProcessorCount + " cores\r\n" +
                           "• الذاكرة: " + (GC.GetTotalMemory(false) / 1024 / 1024) + " MB\r\n\r\n" +
                           "🌍 إعدادات المنطقة:\r\n" +
                           "• اللغة: " + CultureInfo.CurrentCulture.DisplayName + "\r\n" +
                           "• العملة: " + CultureInfo.CurrentCulture.NumberFormat.CurrencySymbol + "\r\n" +
                           "• التقويم: " + CultureInfo.CurrentCulture.Calendar.GetType().Name;

            MessageBox.Show(systemInfo, "معلومات النظام", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // عرض شاشة تسجيل الدخول أولاً
            var loginForm = new LoginForm();
            if (loginForm.ShowDialog() == DialogResult.OK)
            {
                // إذا تم تسجيل الدخول بنجاح، افتح الواجهة الرئيسية
                Application.Run(new SimpleApp());
            }
        }
    }

    // فئة شاشة تسجيل الدخول
    public partial class LoginForm : Form
    {
        private TextBox usernameTextBox;
        private TextBox passwordTextBox;
        private Button loginButton;
        private Button cancelButton;

        public LoginForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "تسجيل الدخول - نظام المحاسبة";
            this.Size = new System.Drawing.Size(450, 300);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Header Panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 80,
                BackColor = System.Drawing.Color.FromArgb(33, 150, 243)
            };

            var headerLabel = new Label
            {
                Text = "🔐 تسجيل الدخول",
                Font = new System.Drawing.Font("Segoe UI", 18, System.Drawing.FontStyle.Bold),
                ForeColor = System.Drawing.Color.White,
                Dock = DockStyle.Fill,
                TextAlign = System.Drawing.ContentAlignment.MiddleCenter
            };

            headerPanel.Controls.Add(headerLabel);
            this.Controls.Add(headerPanel);

            // Main Panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(30),
                BackColor = System.Drawing.Color.White
            };

            // Username Label
            var usernameLabel = new Label
            {
                Text = "👤 اسم المستخدم:",
                Location = new System.Drawing.Point(0, 20),
                Size = new System.Drawing.Size(120, 25),
                Font = new System.Drawing.Font("Segoe UI", 10, System.Drawing.FontStyle.Bold),
                TextAlign = System.Drawing.ContentAlignment.MiddleRight
            };

            // Username TextBox
            usernameTextBox = new TextBox
            {
                Location = new System.Drawing.Point(130, 20),
                Size = new System.Drawing.Size(200, 25),
                Font = new System.Drawing.Font("Segoe UI", 10),
                Text = "admin" // قيمة افتراضية
            };

            // Password Label
            var passwordLabel = new Label
            {
                Text = "🔑 كلمة المرور:",
                Location = new System.Drawing.Point(0, 60),
                Size = new System.Drawing.Size(120, 25),
                Font = new System.Drawing.Font("Segoe UI", 10, System.Drawing.FontStyle.Bold),
                TextAlign = System.Drawing.ContentAlignment.MiddleRight
            };

            // Password TextBox
            passwordTextBox = new TextBox
            {
                Location = new System.Drawing.Point(130, 60),
                Size = new System.Drawing.Size(200, 25),
                Font = new System.Drawing.Font("Segoe UI", 10),
                PasswordChar = '*',
                Text = "admin123" // قيمة افتراضية
            };

            // Login Button
            loginButton = new Button
            {
                Text = "✅ دخول",
                Location = new System.Drawing.Point(130, 110),
                Size = new System.Drawing.Size(90, 35),
                BackColor = System.Drawing.Color.FromArgb(40, 167, 69),
                ForeColor = System.Drawing.Color.White,
                Font = new System.Drawing.Font("Segoe UI", 10, System.Drawing.FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };

            loginButton.Click += LoginButton_Click;

            // Cancel Button
            cancelButton = new Button
            {
                Text = "❌ إلغاء",
                Location = new System.Drawing.Point(240, 110),
                Size = new System.Drawing.Size(90, 35),
                BackColor = System.Drawing.Color.FromArgb(220, 53, 69),
                ForeColor = System.Drawing.Color.White,
                Font = new System.Drawing.Font("Segoe UI", 10, System.Drawing.FontStyle.Bold),
                FlatStyle = FlatStyle.Flat
            };

            cancelButton.Click += (s, e) => this.DialogResult = DialogResult.Cancel;

            // Info Label
            var infoLabel = new Label
            {
                Text = "💡 المعلومات الافتراضية:\r\nاسم المستخدم: admin\r\nكلمة المرور: admin123",
                Location = new System.Drawing.Point(0, 160),
                Size = new System.Drawing.Size(350, 50),
                Font = new System.Drawing.Font("Segoe UI", 8),
                ForeColor = System.Drawing.Color.Gray,
                TextAlign = System.Drawing.ContentAlignment.TopCenter
            };

            mainPanel.Controls.AddRange(new Control[] {
                usernameLabel, usernameTextBox, passwordLabel, passwordTextBox,
                loginButton, cancelButton, infoLabel
            });

            this.Controls.Add(mainPanel);

            // تعيين زر الدخول كزر افتراضي
            this.AcceptButton = loginButton;
            this.CancelButton = cancelButton;
        }

        private void LoginButton_Click(object sender, EventArgs e)
        {
            var username = usernameTextBox.Text.Trim();
            var password = passwordTextBox.Text.Trim();

            // التحقق من بيانات تسجيل الدخول
            if (string.IsNullOrEmpty(username) || string.IsNullOrEmpty(password))
            {
                MessageBox.Show("⚠️ يرجى إدخال اسم المستخدم وكلمة المرور", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // التحقق من صحة البيانات (يمكن تطويرها لاحقاً)
            if (username == "admin" && password == "admin123")
            {
                MessageBox.Show("✅ تم تسجيل الدخول بنجاح!\r\n\r\n" +
                              "🎉 مرحباً بك في نظام المحاسبة المتقدم", "نجح تسجيل الدخول",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
                this.DialogResult = DialogResult.OK;
            }
            else
            {
                MessageBox.Show("❌ اسم المستخدم أو كلمة المرور غير صحيحة!\r\n\r\n" +
                              "💡 تأكد من:\r\n" +
                              "• اسم المستخدم: admin\r\n" +
                              "• كلمة المرور: admin123", "خطأ في تسجيل الدخول",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);

                // مسح كلمة المرور وإعادة التركيز
                passwordTextBox.Clear();
                passwordTextBox.Focus();
            }
        }
    }
}
