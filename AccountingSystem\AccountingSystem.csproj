<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>AccountingSystem</RootNamespace>
    <AssemblyName>AccountingSystem</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <ProjectTypeGuids>{60dc8134-eba5-43b8-bcc9-bb4bc16c2548};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <WarningLevel>4</WarningLevel>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.Xaml">
      <RequiredTargetFramework>4.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="MaterialDesignThemes.Wpf">
      <HintPath>packages\MaterialDesignThemes.5.2.1\lib\net462\MaterialDesignThemes.Wpf.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="MaterialDesignColors">
      <HintPath>packages\MaterialDesignColors.5.2.1\lib\net462\MaterialDesignColors.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework">
      <HintPath>packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer">
      <HintPath>packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SQLite">
      <HintPath>packages\System.Data.SQLite.Core.1.0.119.0\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Data.SQLite.EF6">
      <HintPath>packages\System.Data.SQLite.EF6.1.0.119.0\lib\net46\System.Data.SQLite.EF6.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Xaml.Behaviors">
      <HintPath>packages\Microsoft.Xaml.Behaviors.Wpf.1.1.39\lib\net45\Microsoft.Xaml.Behaviors.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ApplicationDefinition Include="App.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </ApplicationDefinition>
    <Page Include="Views\MainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AccountsView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddEditAccountWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\JournalEntriesView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddEditJournalEntryWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ViewJournalEntryWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\InvoicesView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddEditInvoiceWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ViewInvoiceWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddPaymentWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ReportsView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ReportDateSelectionWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\LoginWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\UserManagementView.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ReportViewerWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AccountSelectionWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\CustomPeriodSelectionWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\SimpleMainWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AccountsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddAccountWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\JournalEntriesWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddJournalEntryWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\InvoicesWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddInvoiceWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ReportsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\ItemsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddItemWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\SuppliersWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddSupplierCustomerWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\SettingsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\AddCurrencyWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\SimpleSettingsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Page Include="Views\NumberSettingsWindow.xaml">
      <Generator>MSBuild:Compile</Generator>
      <SubType>Designer</SubType>
    </Page>
    <Compile Include="App.xaml.cs">
      <DependentUpon>App.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\MainWindow.xaml.cs">
      <DependentUpon>MainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AccountsView.xaml.cs">
      <DependentUpon>AccountsView.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddEditAccountWindow.xaml.cs">
      <DependentUpon>AddEditAccountWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\JournalEntriesView.xaml.cs">
      <DependentUpon>JournalEntriesView.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddEditJournalEntryWindow.xaml.cs">
      <DependentUpon>AddEditJournalEntryWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\ViewJournalEntryWindow.xaml.cs">
      <DependentUpon>ViewJournalEntryWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\InvoicesView.xaml.cs">
      <DependentUpon>InvoicesView.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddEditInvoiceWindow.xaml.cs">
      <DependentUpon>AddEditInvoiceWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\ViewInvoiceWindow.xaml.cs">
      <DependentUpon>ViewInvoiceWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddPaymentWindow.xaml.cs">
      <DependentUpon>AddPaymentWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\ReportsView.xaml.cs">
      <DependentUpon>ReportsView.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\ReportDateSelectionWindow.xaml.cs">
      <DependentUpon>ReportDateSelectionWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\LoginWindow.xaml.cs">
      <DependentUpon>LoginWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\UserManagementView.xaml.cs">
      <DependentUpon>UserManagementView.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\ReportViewerWindow.xaml.cs">
      <DependentUpon>ReportViewerWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AccountSelectionWindow.xaml.cs">
      <DependentUpon>AccountSelectionWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\CustomPeriodSelectionWindow.xaml.cs">
      <DependentUpon>CustomPeriodSelectionWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\SimpleMainWindow.xaml.cs">
      <DependentUpon>SimpleMainWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AccountsWindow.xaml.cs">
      <DependentUpon>AccountsWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddAccountWindow.xaml.cs">
      <DependentUpon>AddAccountWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\JournalEntriesWindow.xaml.cs">
      <DependentUpon>JournalEntriesWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddJournalEntryWindow.xaml.cs">
      <DependentUpon>AddJournalEntryWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\InvoicesWindow.xaml.cs">
      <DependentUpon>InvoicesWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddInvoiceWindow.xaml.cs">
      <DependentUpon>AddInvoiceWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\ReportsWindow.xaml.cs">
      <DependentUpon>ReportsWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\ItemsWindow.xaml.cs">
      <DependentUpon>ItemsWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddItemWindow.xaml.cs">
      <DependentUpon>AddItemWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\SuppliersWindow.xaml.cs">
      <DependentUpon>SuppliersWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddSupplierCustomerWindow.xaml.cs">
      <DependentUpon>AddSupplierCustomerWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\SettingsWindow.xaml.cs">
      <DependentUpon>SettingsWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\AddCurrencyWindow.xaml.cs">
      <DependentUpon>AddCurrencyWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\SimpleSettingsWindow.xaml.cs">
      <DependentUpon>SimpleSettingsWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Views\NumberSettingsWindow.xaml.cs">
      <DependentUpon>NumberSettingsWindow.xaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>

    <!-- Models -->
    <Compile Include="Models\Account.cs" />
    <Compile Include="Models\User.cs" />
    <Compile Include="Models\JournalEntry.cs" />
    <Compile Include="Models\Invoice.cs" />
    <Compile Include="Models\ReportModels.cs" />

    <!-- ViewModels -->
    <Compile Include="ViewModels\ViewModelBase.cs" />
    <Compile Include="ViewModels\MainWindowViewModel.cs" />
    <Compile Include="ViewModels\LoginViewModel.cs" />
    <Compile Include="ViewModels\AccountsViewModel.cs" />
    <Compile Include="ViewModels\AddEditAccountViewModel.cs" />
    <Compile Include="ViewModels\JournalEntriesViewModel.cs" />
    <Compile Include="ViewModels\AddEditJournalEntryViewModel.cs" />
    <Compile Include="ViewModels\ViewJournalEntryViewModel.cs" />
    <Compile Include="ViewModels\InvoicesViewModel.cs" />
    <Compile Include="ViewModels\AddEditInvoiceViewModel.cs" />
    <Compile Include="ViewModels\ViewInvoiceViewModel.cs" />
    <Compile Include="ViewModels\AddPaymentViewModel.cs" />
    <Compile Include="ViewModels\ReportsViewModel.cs" />
    <Compile Include="ViewModels\ReportDateSelectionViewModel.cs" />
    <Compile Include="ViewModels\UserManagementViewModel.cs" />
    <Compile Include="ViewModels\ReportViewerViewModel.cs" />
    <Compile Include="ViewModels\AccountSelectionViewModel.cs" />
    <Compile Include="ViewModels\CustomPeriodSelectionViewModel.cs" />

    <!-- Services -->
    <Compile Include="Services\AccountService.cs" />
    <Compile Include="Services\AuthenticationService.cs" />
    <Compile Include="Services\UserManagementService.cs" />
    <Compile Include="Services\JournalEntryService.cs" />
    <Compile Include="Services\InvoiceService.cs" />
    <Compile Include="Services\ReportService.cs" />

    <!-- Data -->
    <Compile Include="Data\AccountingDbContext.cs" />

    <!-- Helpers -->
    <Compile Include="Helpers\RelayCommand.cs" />
    <Compile Include="Helpers\Converters.cs" />
    <Compile Include="Helpers\Constants.cs" />
    <Compile Include="Helpers\DataValidator.cs" />
    <Compile Include="Helpers\BackupManager.cs" />
    <Compile Include="Helpers\PerformanceOptimizer.cs" />
    <Compile Include="Helpers\SystemFormatHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Properties\AssemblyInfo.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
