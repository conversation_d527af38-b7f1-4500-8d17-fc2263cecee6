﻿#pragma checksum "..\..\..\Views\AddInvoiceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "95EE006AC0406162E9B4AC671CFFE2B62D5B436A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem.Views {
    
    
    /// <summary>
    /// AddInvoiceWindow
    /// </summary>
    public partial class AddInvoiceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border HeaderBorder;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderIcon;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTitle;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceDateTextBox;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerSupplierLabel;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CustomerSupplierComboBox;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoiceItemsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtotalLabel;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscountTextBox;
        
        #line default
        #line hidden
        
        
        #line 184 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TaxLabel;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalLabel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem;component/views/addinvoicewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\AddInvoiceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 2:
            this.HeaderIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.HeaderTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.InvoiceNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.InvoiceDateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.CustomerSupplierLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CustomerSupplierComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.InvoiceItemsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 11:
            
            #line 135 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 139 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ScanBarcodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 143 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectFromInventoryButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 147 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.SubtotalLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.DiscountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.TaxLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.TotalLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            
            #line 211 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveDraftButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 215 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAndSendButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 219 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 223 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

