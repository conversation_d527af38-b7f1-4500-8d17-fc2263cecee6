﻿#pragma checksum "..\..\..\Views\AddInvoiceWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "FF3D2358E1B2046F90AB1987A54EB6EB4382E2B932690AB27CECC65E49D21562"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem.Views {
    
    
    /// <summary>
    /// AddInvoiceWindow
    /// </summary>
    public partial class AddInvoiceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border HeaderBorder;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderIcon;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTitle;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InvoiceDateTextBox;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CustomerSupplierLabel;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CustomerSupplierComboBox;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PaymentMethodComboBox;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotesTextBox;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoiceItemsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SubtotalLabel;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DiscountTextBox;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TaxLabel;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\Views\AddInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalLabel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem;component/views/addinvoicewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\AddInvoiceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 2:
            this.HeaderIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.HeaderTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.InvoiceNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.InvoiceDateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.CustomerSupplierLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.CustomerSupplierComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 8:
            this.PaymentMethodComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.NotesTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.InvoiceItemsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 11:
            
            #line 134 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 138 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RemoveItemButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.SubtotalLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.DiscountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.TaxLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.TotalLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            
            #line 202 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveDraftButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 206 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAndSendButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 210 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 214 "..\..\..\Views\AddInvoiceWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

