using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingSystem.Diagnostic
{
    public partial class TestWindow : Window
    {
        public TestWindow()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Title = "Diagnostic Test - Login";
            this.Width = 500;
            this.Height = 400;
            this.WindowStartupLocation = WindowStartupLocation.CenterScreen;
            this.Background = System.Windows.Media.Brushes.White;

            var grid = new Grid();
            this.Content = grid;

            // Create UI elements
            var stackPanel = new StackPanel
            {
                Margin = new Thickness(50),
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center
            };

            var title = new TextBlock
            {
                Text = "Diagnostic Login Test",
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 0, 0, 30)
            };

            var usernameLabel = new TextBlock
            {
                Text = "Username:",
                FontSize = 16,
                Margin = new Thickness(0, 0, 0, 5)
            };

            var usernameBox = new TextBox
            {
                Name = "UsernameBox",
                Width = 300,
                Height = 35,
                FontSize = 14,
                Text = "admin",
                Margin = new Thickness(0, 0, 0, 20)
            };

            var passwordLabel = new TextBlock
            {
                Text = "Password:",
                FontSize = 16,
                Margin = new Thickness(0, 0, 0, 5)
            };

            var passwordBox = new PasswordBox
            {
                Name = "PasswordBox",
                Width = 300,
                Height = 35,
                FontSize = 14,
                Password = "admin123",
                Margin = new Thickness(0, 0, 0, 30)
            };

            var loginButton = new Button
            {
                Content = "LOGIN TEST",
                Width = 300,
                Height = 50,
                FontSize = 18,
                FontWeight = FontWeights.Bold,
                Background = System.Windows.Media.Brushes.Blue,
                Foreground = System.Windows.Media.Brushes.White,
                Margin = new Thickness(0, 0, 0, 20)
            };

            var statusLabel = new TextBlock
            {
                Name = "StatusLabel",
                FontSize = 14,
                HorizontalAlignment = HorizontalAlignment.Center,
                Margin = new Thickness(0, 10, 0, 0)
            };

            // Add click event
            loginButton.Click += (sender, e) =>
            {
                try
                {
                    if (usernameBox.Text == "admin" && passwordBox.Password == "admin123")
                    {
                        statusLabel.Text = "✅ LOGIN SUCCESSFUL!";
                        statusLabel.Foreground = System.Windows.Media.Brushes.Green;
                        
                        // Try to open main window
                        try
                        {
                            var mainWindow = new AccountingSystem.Views.MainWindow();
                            mainWindow.Show();
                            this.Close();
                        }
                        catch (Exception ex)
                        {
                            statusLabel.Text = $"❌ Error opening main window: {ex.Message}";
                            statusLabel.Foreground = System.Windows.Media.Brushes.Red;
                        }
                    }
                    else
                    {
                        statusLabel.Text = "❌ Invalid credentials";
                        statusLabel.Foreground = System.Windows.Media.Brushes.Red;
                    }
                }
                catch (Exception ex)
                {
                    statusLabel.Text = $"❌ Error: {ex.Message}";
                    statusLabel.Foreground = System.Windows.Media.Brushes.Red;
                }
            };

            // Add elements to stack panel
            stackPanel.Children.Add(title);
            stackPanel.Children.Add(usernameLabel);
            stackPanel.Children.Add(usernameBox);
            stackPanel.Children.Add(passwordLabel);
            stackPanel.Children.Add(passwordBox);
            stackPanel.Children.Add(loginButton);
            stackPanel.Children.Add(statusLabel);

            grid.Children.Add(stackPanel);
        }
    }

    public class DiagnosticApp : Application
    {
        [STAThread]
        public static void Main()
        {
            var app = new DiagnosticApp();
            app.Run(new TestWindow());
        }
    }
}
