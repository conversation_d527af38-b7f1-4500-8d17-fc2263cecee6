using System;
using System.Data.Entity;
using System.Data.Entity.ModelConfiguration.Conventions;
using System.Linq;
using AccountingSystem.Models;

namespace AccountingSystem.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي
    /// </summary>
    public class AccountingDbContext : DbContext
    {
        public AccountingDbContext() : base("DefaultConnection")
        {
            Database.SetInitializer(new AccountingDbInitializer());
        }

        public AccountingDbContext(System.Data.Common.DbConnection connection, bool contextOwnsConnection)
            : base(connection, contextOwnsConnection)
        {
            Database.SetInitializer(new AccountingDbInitializer());
        }

        // DbSets
        public virtual DbSet<Account> Accounts { get; set; }
        public virtual DbSet<JournalEntry> JournalEntries { get; set; }
        public virtual DbSet<JournalEntryDetail> JournalEntryDetails { get; set; }
        public virtual DbSet<Invoice> Invoices { get; set; }
        public virtual DbSet<InvoiceDetail> InvoiceDetails { get; set; }
        public virtual DbSet<Payment> Payments { get; set; }
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<Permission> Permissions { get; set; }
        public virtual DbSet<UserPermission> UserPermissions { get; set; }
        public virtual DbSet<AuditLog> AuditLogs { get; set; }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            // Remove pluralizing table name convention
            modelBuilder.Conventions.Remove<PluralizingTableNameConvention>();

            // Configure Account entity
            modelBuilder.Entity<Account>()
                .HasOptional(a => a.ParentAccount)
                .WithMany(a => a.SubAccounts)
                .HasForeignKey(a => a.ParentAccountId)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<Account>()
                .HasIndex(a => a.AccountNumber)
                .IsUnique();

            // Configure JournalEntry entity
            modelBuilder.Entity<JournalEntry>()
                .HasIndex(je => je.EntryNumber)
                .IsUnique();

            modelBuilder.Entity<JournalEntry>()
                .HasMany(je => je.JournalEntryDetails)
                .WithRequired(jed => jed.JournalEntry)
                .HasForeignKey(jed => jed.JournalEntryId)
                .WillCascadeOnDelete(true);

            // Configure JournalEntryDetail entity
            modelBuilder.Entity<JournalEntryDetail>()
                .HasRequired(jed => jed.Account)
                .WithMany(a => a.JournalEntryDetails)
                .HasForeignKey(jed => jed.AccountId)
                .WillCascadeOnDelete(false);

            // Configure Invoice entity
            modelBuilder.Entity<Invoice>()
                .HasIndex(i => i.InvoiceNumber)
                .IsUnique();

            modelBuilder.Entity<Invoice>()
                .HasRequired(i => i.CustomerSupplierAccount)
                .WithMany()
                .HasForeignKey(i => i.CustomerSupplierAccountId)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<Invoice>()
                .HasOptional(i => i.JournalEntry)
                .WithMany()
                .HasForeignKey(i => i.JournalEntryId)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<Invoice>()
                .HasMany(i => i.InvoiceDetails)
                .WithRequired(id => id.Invoice)
                .HasForeignKey(id => id.InvoiceId)
                .WillCascadeOnDelete(true);

            modelBuilder.Entity<Invoice>()
                .HasMany(i => i.Payments)
                .WithRequired(p => p.Invoice)
                .HasForeignKey(p => p.InvoiceId)
                .WillCascadeOnDelete(true);

            // Configure User entity
            modelBuilder.Entity<User>()
                .HasIndex(u => u.Username)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasIndex(u => u.Email)
                .IsUnique();

            modelBuilder.Entity<User>()
                .HasMany(u => u.UserPermissions)
                .WithRequired(up => up.User)
                .HasForeignKey(up => up.UserId)
                .WillCascadeOnDelete(true);

            modelBuilder.Entity<User>()
                .HasMany(u => u.AuditLogs)
                .WithRequired(al => al.User)
                .HasForeignKey(al => al.UserId)
                .WillCascadeOnDelete(false);

            // Configure Permission entity
            modelBuilder.Entity<Permission>()
                .HasIndex(p => p.PermissionName)
                .IsUnique();

            modelBuilder.Entity<Permission>()
                .HasMany(p => p.UserPermissions)
                .WithRequired(up => up.Permission)
                .HasForeignKey(up => up.PermissionId)
                .WillCascadeOnDelete(true);

            // Configure UserPermission entity
            modelBuilder.Entity<UserPermission>()
                .HasIndex(up => new { up.UserId, up.PermissionId })
                .IsUnique();

            base.OnModelCreating(modelBuilder);
        }

        public override int SaveChanges()
        {
            // Add audit logging here if needed
            var modifiedEntries = ChangeTracker.Entries()
                .Where(x => x.Entity is IAuditable && (x.State == EntityState.Added || x.State == EntityState.Modified));

            foreach (var entry in modifiedEntries)
            {
                var entity = entry.Entity as IAuditable;
                if (entity != null)
                {
                    if (entry.State == EntityState.Added)
                    {
                        entity.CreatedDate = DateTime.Now;
                        // entity.CreatedBy = CurrentUser; // Set from current user context
                    }
                    else if (entry.State == EntityState.Modified)
                    {
                        entity.ModifiedDate = DateTime.Now;
                        // entity.ModifiedBy = CurrentUser; // Set from current user context
                    }
                }
            }

            return base.SaveChanges();
        }
    }

    /// <summary>
    /// واجهة للكيانات القابلة للمراجعة
    /// </summary>
    public interface IAuditable
    {
        DateTime CreatedDate { get; set; }
        DateTime? ModifiedDate { get; set; }
        string CreatedBy { get; set; }
        string ModifiedBy { get; set; }
    }

    /// <summary>
    /// مُهيئ قاعدة البيانات
    /// </summary>
    public class AccountingDbInitializer : CreateDatabaseIfNotExists<AccountingDbContext>
    {
        protected override void Seed(AccountingDbContext context)
        {
            // Create default admin user
            var adminUser = new User
            {
                Username = "admin",
                FullName = "مدير النظام",
                Email = "<EMAIL>",
                Role = UserRole.SystemAdmin,
                IsActive = true,
                CreatedBy = "System"
            };
            adminUser.SetPassword("admin123");
            context.Users.Add(adminUser);

            // Create default permissions
            var permissions = new[]
            {
                new Permission { PermissionName = "ViewAccounts", Description = "عرض الحسابات", Category = "Accounts" },
                new Permission { PermissionName = "AddAccounts", Description = "إضافة حسابات", Category = "Accounts" },
                new Permission { PermissionName = "EditAccounts", Description = "تعديل الحسابات", Category = "Accounts" },
                new Permission { PermissionName = "DeleteAccounts", Description = "حذف الحسابات", Category = "Accounts" },
                new Permission { PermissionName = "ViewJournalEntries", Description = "عرض القيود", Category = "JournalEntries" },
                new Permission { PermissionName = "AddJournalEntries", Description = "إضافة قيود", Category = "JournalEntries" },
                new Permission { PermissionName = "EditJournalEntries", Description = "تعديل القيود", Category = "JournalEntries" },
                new Permission { PermissionName = "DeleteJournalEntries", Description = "حذف القيود", Category = "JournalEntries" },
                new Permission { PermissionName = "PostJournalEntries", Description = "ترحيل القيود", Category = "JournalEntries" },
                new Permission { PermissionName = "ViewInvoices", Description = "عرض الفواتير", Category = "Invoices" },
                new Permission { PermissionName = "AddInvoices", Description = "إضافة فواتير", Category = "Invoices" },
                new Permission { PermissionName = "EditInvoices", Description = "تعديل الفواتير", Category = "Invoices" },
                new Permission { PermissionName = "DeleteInvoices", Description = "حذف الفواتير", Category = "Invoices" },
                new Permission { PermissionName = "ViewReports", Description = "عرض التقارير", Category = "Reports" },
                new Permission { PermissionName = "ExportReports", Description = "تصدير التقارير", Category = "Reports" },
                new Permission { PermissionName = "ManageUsers", Description = "إدارة المستخدمين", Category = "Administration" },
                new Permission { PermissionName = "ManagePermissions", Description = "إدارة الصلاحيات", Category = "Administration" },
                new Permission { PermissionName = "ViewAuditLogs", Description = "عرض سجل المراجعة", Category = "Administration" }
            };

            context.Permissions.AddRange(permissions);
            context.SaveChanges();

            // Grant all permissions to admin user
            foreach (var permission in permissions)
            {
                context.UserPermissions.Add(new UserPermission
                {
                    UserId = adminUser.UserId,
                    PermissionId = permission.PermissionId,
                    IsGranted = true,
                    GrantedBy = "System"
                });
            }

            // Create default chart of accounts
            CreateDefaultChartOfAccounts(context);

            context.SaveChanges();
            base.Seed(context);
        }

        private void CreateDefaultChartOfAccounts(AccountingDbContext context)
        {
            var accounts = new[]
            {
                // Assets
                new Account { AccountNumber = "1000", AccountName = "الأصول", AccountType = AccountType.Assets, BalanceNature = BalanceNature.Debit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "1100", AccountName = "الأصول المتداولة", AccountType = AccountType.Assets, BalanceNature = BalanceNature.Debit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "1110", AccountName = "النقدية", AccountType = AccountType.Assets, BalanceNature = BalanceNature.Debit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "1120", AccountName = "العملاء", AccountType = AccountType.Assets, BalanceNature = BalanceNature.Debit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "1130", AccountName = "المخزون", AccountType = AccountType.Assets, BalanceNature = BalanceNature.Debit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                
                // Liabilities
                new Account { AccountNumber = "2000", AccountName = "الخصوم", AccountType = AccountType.Liabilities, BalanceNature = BalanceNature.Credit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "2100", AccountName = "الخصوم المتداولة", AccountType = AccountType.Liabilities, BalanceNature = BalanceNature.Credit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "2110", AccountName = "الموردون", AccountType = AccountType.Liabilities, BalanceNature = BalanceNature.Credit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "2120", AccountName = "ضريبة القيمة المضافة", AccountType = AccountType.Liabilities, BalanceNature = BalanceNature.Credit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                
                // Equity
                new Account { AccountNumber = "3000", AccountName = "حقوق الملكية", AccountType = AccountType.Equity, BalanceNature = BalanceNature.Credit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "3100", AccountName = "رأس المال", AccountType = AccountType.Equity, BalanceNature = BalanceNature.Credit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "3200", AccountName = "الأرباح المحتجزة", AccountType = AccountType.Equity, BalanceNature = BalanceNature.Credit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                
                // Revenue
                new Account { AccountNumber = "4000", AccountName = "الإيرادات", AccountType = AccountType.Revenue, BalanceNature = BalanceNature.Credit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "4100", AccountName = "إيرادات المبيعات", AccountType = AccountType.Revenue, BalanceNature = BalanceNature.Credit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                
                // Expenses
                new Account { AccountNumber = "5000", AccountName = "المصروفات", AccountType = AccountType.Expenses, BalanceNature = BalanceNature.Debit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "5100", AccountName = "تكلفة البضاعة المباعة", AccountType = AccountType.Expenses, BalanceNature = BalanceNature.Debit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" },
                new Account { AccountNumber = "5200", AccountName = "المصروفات التشغيلية", AccountType = AccountType.Expenses, BalanceNature = BalanceNature.Debit, OpeningBalance = 0, CurrentBalance = 0, CreatedBy = "System" }
            };

            context.Accounts.AddRange(accounts);
            context.SaveChanges();

            // Set parent-child relationships
            var assetsMain = context.Accounts.First(a => a.AccountNumber == "1000");
            var currentAssets = context.Accounts.First(a => a.AccountNumber == "1100");
            var cash = context.Accounts.First(a => a.AccountNumber == "1110");
            var customers = context.Accounts.First(a => a.AccountNumber == "1120");
            var inventory = context.Accounts.First(a => a.AccountNumber == "1130");

            currentAssets.ParentAccountId = assetsMain.AccountId;
            cash.ParentAccountId = currentAssets.AccountId;
            customers.ParentAccountId = currentAssets.AccountId;
            inventory.ParentAccountId = currentAssets.AccountId;

            var liabilitiesMain = context.Accounts.First(a => a.AccountNumber == "2000");
            var currentLiabilities = context.Accounts.First(a => a.AccountNumber == "2100");
            var suppliers = context.Accounts.First(a => a.AccountNumber == "2110");
            var vat = context.Accounts.First(a => a.AccountNumber == "2120");

            currentLiabilities.ParentAccountId = liabilitiesMain.AccountId;
            suppliers.ParentAccountId = currentLiabilities.AccountId;
            vat.ParentAccountId = currentLiabilities.AccountId;

            var equityMain = context.Accounts.First(a => a.AccountNumber == "3000");
            var capital = context.Accounts.First(a => a.AccountNumber == "3100");
            var retainedEarnings = context.Accounts.First(a => a.AccountNumber == "3200");

            capital.ParentAccountId = equityMain.AccountId;
            retainedEarnings.ParentAccountId = equityMain.AccountId;

            var revenueMain = context.Accounts.First(a => a.AccountNumber == "4000");
            var salesRevenue = context.Accounts.First(a => a.AccountNumber == "4100");

            salesRevenue.ParentAccountId = revenueMain.AccountId;

            var expensesMain = context.Accounts.First(a => a.AccountNumber == "5000");
            var cogs = context.Accounts.First(a => a.AccountNumber == "5100");
            var operatingExpenses = context.Accounts.First(a => a.AccountNumber == "5200");

            cogs.ParentAccountId = expensesMain.AccountId;
            operatingExpenses.ParentAccountId = expensesMain.AccountId;
        }
    }
}
