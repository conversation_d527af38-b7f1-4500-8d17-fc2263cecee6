<Window x:Class="AccountingSystem.Views.AddInvoiceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة فاتورة جديدة - نظام المحاسبة المتقدم" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" x:Name="HeaderBorder" Background="#E91E63">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock x:Name="HeaderIcon" Text="🧾" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock x:Name="HeaderTitle" Text="إضافة فاتورة مبيعات جديدة" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="30,20">
                
                <!-- Invoice Header Info -->
                <Border Background="#F5F5F5" CornerRadius="5" Padding="20" Margin="0,0,0,20">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <Grid Grid.Row="0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="رقم الفاتورة *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="InvoiceNumberTextBox" 
                                         Height="35" FontSize="14" Padding="10"
                                         Background="White" IsReadOnly="True"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0">
                                <TextBlock Text="التاريخ *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="InvoiceDateTextBox" 
                                         Height="35" FontSize="14" Padding="10"
                                         Background="White"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock x:Name="CustomerSupplierLabel" Text="العميل *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="CustomerSupplierComboBox"
                                          Height="35" FontSize="14" Padding="10"
                                          Background="White">
                                    <ComboBoxItem Content="شركة الأمل التجارية"/>
                                    <ComboBoxItem Content="متجر الفجر"/>
                                    <ComboBoxItem Content="شركة البناء الحديث"/>
                                    <ComboBoxItem Content="مكتبة المعرفة"/>
                                    <ComboBoxItem Content="مطعم الذواقة"/>
                                </ComboBox>
                            </StackPanel>
                        </Grid>
                        
                        <Grid Grid.Row="1" Margin="0,15,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="طريقة الدفع" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="PaymentMethodComboBox"
                                          Height="35" FontSize="14" Padding="10"
                                          Background="White">
                                    <ComboBoxItem Content="نقداً" IsSelected="True"/>
                                    <ComboBoxItem Content="آجل"/>
                                    <ComboBoxItem Content="شيك"/>
                                    <ComboBoxItem Content="تحويل بنكي"/>
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                <TextBlock Text="ملاحظات" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="NotesTextBox" 
                                         Height="35" FontSize="14" Padding="10"
                                         Background="White"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </Border>
                
                <!-- Invoice Items -->
                <TextBlock Text="أصناف الفاتورة" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                
                <Border BorderBrush="#DDD" BorderThickness="1" CornerRadius="5" Margin="0,0,0,10">
                    <DataGrid x:Name="InvoiceItemsDataGrid" 
                              AutoGenerateColumns="False"
                              CanUserAddRows="True"
                              CanUserDeleteRows="True"
                              GridLinesVisibility="All"
                              HeadersVisibility="Column"
                              Background="White"
                              FontSize="13"
                              MinHeight="200">
                        
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120"/>
                            <DataGridTextColumn Header="الصنف *" Binding="{Binding ItemName}" Width="*"/>
                            <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                            <DataGridTextColumn Header="الكمية *" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="السعر *" Binding="{Binding UnitPrice}" Width="100"/>
                            <DataGridTextColumn Header="الإجمالي" Binding="{Binding Total}" Width="120" IsReadOnly="True"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>
                
                <!-- Add/Remove Item Buttons -->
                <StackPanel Orientation="Horizontal" Margin="0,10,0,20">
                    <Button Content="➕ إضافة صنف"
                            Width="120" Height="35" FontSize="13"
                            Background="#4CAF50" Foreground="White" Margin="0,0,10,0"
                            Click="AddItemButton_Click"/>
                    <Button Content="📷 مسح باركود"
                            Width="120" Height="35" FontSize="13"
                            Background="#2196F3" Foreground="White" Margin="0,0,10,0"
                            Click="ScanBarcodeButton_Click"/>
                    <Button Content="📦 اختيار من المخزون"
                            Width="140" Height="35" FontSize="13"
                            Background="#9C27B0" Foreground="White" Margin="0,0,10,0"
                            Click="SelectFromInventoryButton_Click"/>
                    <Button Content="➖ حذف صنف"
                            Width="120" Height="35" FontSize="13"
                            Background="#F44336" Foreground="White"
                            Click="RemoveItemButton_Click"/>
                </StackPanel>
                
                <!-- Invoice Totals -->
                <Border Background="#E3F2FD" CornerRadius="5" Padding="20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="200"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="1">
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="المجموع الفرعي:" FontSize="14"/>
                                <TextBlock Grid.Column="1" x:Name="SubtotalLabel" Text="0.00" FontSize="14" FontWeight="Bold" HorizontalAlignment="Right"/>
                            </Grid>
                            
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="الخصم:" FontSize="14"/>
                                <TextBox Grid.Column="1" x:Name="DiscountTextBox" Text="0.00" FontSize="14"
                                         Height="25" Padding="5"/>
                            </Grid>
                            
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="الضريبة (15%):" FontSize="14"/>
                                <TextBlock Grid.Column="1" x:Name="TaxLabel" Text="0.00" FontSize="14" FontWeight="Bold" HorizontalAlignment="Right"/>
                            </Grid>
                            
                            <Separator Margin="0,10"/>
                            
                            <Grid Margin="0,5">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="100"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="الإجمالي النهائي:" FontSize="16" FontWeight="Bold"/>
                                <TextBlock Grid.Column="1" x:Name="TotalLabel" Text="0.00" FontSize="16" FontWeight="Bold" 
                                           HorizontalAlignment="Right" Foreground="#E91E63"/>
                            </Grid>
                        </StackPanel>
                    </Grid>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ كمسودة" 
                        Width="140" Height="40" FontSize="14"
                        Background="#FF9800" Foreground="White" Margin="10,0"
                        Click="SaveDraftButton_Click"/>
                <Button Content="✅ حفظ وإرسال" 
                        Width="140" Height="40" FontSize="14" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White" Margin="10,0"
                        Click="SaveAndSendButton_Click"/>
                <Button Content="📄 معاينة وطباعة" 
                        Width="140" Height="40" FontSize="14"
                        Background="#2196F3" Foreground="White" Margin="10,0"
                        Click="PreviewButton_Click"/>
                <Button Content="❌ إلغاء" 
                        Width="140" Height="40" FontSize="14"
                        Background="#F44336" Foreground="White" Margin="10,0"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
