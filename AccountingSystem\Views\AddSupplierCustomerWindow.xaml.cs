using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class AddSupplierCustomerWindow : Window
    {
        private string entityType;

        public AddSupplierCustomerWindow(string type)
        {
            InitializeComponent();
            entityType = type;
            InitializeWindow();
            GenerateCode();
        }

        private void InitializeWindow()
        {
            if (entityType == "عميل")
            {
                HeaderTitle.Text = "إضافة عميل جديد";
                HeaderIcon.Text = "👥";
                HeaderBorder.Background = new SolidColorBrush(Color.FromRgb(33, 150, 243)); // Blue
                NameLabel.Text = "اسم العميل *";
                Title = "إضافة عميل جديد - نظام المحاسبة المتقدم";
            }
        }

        private void GenerateCode()
        {
            var random = new Random();
            var prefix = entityType == "مورد" ? "SUP" : "CUS";
            var code = $"{prefix}{random.Next(100, 999):D3}";
            CodeTextBox.Text = code;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                var message = $"تم حفظ {entityType} بنجاح!\n\n" +
                             $"الكود: {CodeTextBox.Text}\n" +
                             $"الاسم: {NameTextBox.Text}\n" +
                             $"الهاتف: {PhoneTextBox.Text}\n" +
                             $"البريد الإلكتروني: {EmailTextBox.Text}\n" +
                             $"المدينة: {((ComboBoxItem)CityComboBox.SelectedItem)?.Content}\n" +
                             $"الرصيد الافتتاحي: {OpeningBalanceTextBox.Text} ر.س";

                MessageBox.Show(message, $"تم حفظ {entityType}", MessageBoxButton.OK, MessageBoxImage.Information);
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد مسح جميع الحقول؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                ClearAllFields();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء العملية؟ سيتم فقدان البيانات المدخلة.", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result == MessageBoxResult.Yes)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(NameTextBox.Text))
            {
                MessageBox.Show($"يرجى إدخال اسم {entityType}", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                NameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(PhoneTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الهاتف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PhoneTextBox.Focus();
                return false;
            }

            if (CityComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المدينة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CityComboBox.Focus();
                return false;
            }

            // Validate email format if provided
            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                if (!EmailTextBox.Text.Contains("@") || !EmailTextBox.Text.Contains("."))
                {
                    MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    EmailTextBox.Focus();
                    return false;
                }
            }

            // Validate opening balance
            if (!decimal.TryParse(OpeningBalanceTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال رصيد افتتاحي صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                OpeningBalanceTextBox.Focus();
                return false;
            }

            // Validate credit limit
            if (!decimal.TryParse(CreditLimitTextBox.Text, out _))
            {
                MessageBox.Show("يرجى إدخال حد ائتمان صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CreditLimitTextBox.Focus();
                return false;
            }

            // Validate payment terms
            if (!int.TryParse(PaymentTermsTextBox.Text, out int paymentTerms) || paymentTerms < 0)
            {
                MessageBox.Show("يرجى إدخال مدة سداد صحيحة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PaymentTermsTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearAllFields()
        {
            GenerateCode();
            NameTextBox.Clear();
            PhoneTextBox.Clear();
            MobileTextBox.Clear();
            EmailTextBox.Clear();
            CityComboBox.SelectedIndex = -1;
            PostalCodeTextBox.Clear();
            AddressTextBox.Clear();
            OpeningBalanceTextBox.Text = "0.00";
            CreditLimitTextBox.Text = "0.00";
            PaymentTermsTextBox.Text = "30";
            TaxNumberTextBox.Clear();
            CommercialRegisterTextBox.Clear();
            IsActiveCheckBox.IsChecked = true;
            SendNotificationsCheckBox.IsChecked = true;
            AllowCreditCheckBox.IsChecked = true;
            RequireApprovalCheckBox.IsChecked = false;
            
            NameTextBox.Focus();
        }
    }
}
