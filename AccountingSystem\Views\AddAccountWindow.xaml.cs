using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingSystem.Views
{
    public partial class AddAccountWindow : Window
    {
        public AddAccountWindow()
        {
            InitializeComponent();
            GenerateAccountNumber();
        }

        private void GenerateAccountNumber()
        {
            // Generate a random account number for demo
            var random = new Random();
            var accountNumber = random.Next(1000, 9999).ToString();
            AccountNumberTextBox.Text = accountNumber;
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                // Here you would normally save to database
                var message = $"تم حفظ الحساب بنجاح!\n\n" +
                             $"رقم الحساب: {AccountNumberTextBox.Text}\n" +
                             $"اسم الحساب: {AccountNameTextBox.Text}\n" +
                             $"نوع الحساب: {((ComboBoxItem)AccountTypeComboBox.SelectedItem)?.Content}\n" +
                             $"الرصيد الافتتاحي: {InitialBalanceTextBox.Text}\n" +
                             $"العملة: {((ComboBoxItem)CurrencyComboBox.SelectedItem)?.Content}";

                MessageBox.Show(message, "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد مسح جميع الحقول؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                ClearAllFields();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء العملية؟ سيتم فقدان البيانات المدخلة.", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result == MessageBoxResult.Yes)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(AccountNumberTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الحساب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AccountNumberTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(AccountNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الحساب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AccountNameTextBox.Focus();
                return false;
            }

            if (AccountTypeComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الحساب", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                AccountTypeComboBox.Focus();
                return false;
            }

            // Validate initial balance is a number
            if (!string.IsNullOrWhiteSpace(InitialBalanceTextBox.Text))
            {
                if (!decimal.TryParse(InitialBalanceTextBox.Text, out _))
                {
                    MessageBox.Show("يرجى إدخال رقم صحيح للرصيد الافتتاحي", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    InitialBalanceTextBox.Focus();
                    return false;
                }
            }

            return true;
        }

        private void ClearAllFields()
        {
            GenerateAccountNumber();
            AccountNameTextBox.Clear();
            AccountTypeComboBox.SelectedIndex = -1;
            ParentAccountComboBox.SelectedIndex = 0;
            InitialBalanceTextBox.Text = "0.00";
            CurrencyComboBox.SelectedIndex = 0;
            DescriptionTextBox.Clear();
            IsActiveCheckBox.IsChecked = true;
            
            AccountNameTextBox.Focus();
        }
    }
}
