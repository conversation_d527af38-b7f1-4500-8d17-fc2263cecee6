<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  </configSections>
  
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  
  <connectionStrings>
    <add name="DefaultConnection" 
         connectionString="Data Source=AccountingDB.db;Version=3;" 
         providerName="System.Data.SQLite.EF6" />
  </connectionStrings>
  
  <entityFramework>
    <defaultConnectionFactory type="System.Data.SQLite.EF6.SQLiteConnectionFactory, System.Data.SQLite.EF6" />
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
      <provider invariantName="System.Data.SQLite.EF6" type="System.Data.SQLite.EF6.SQLiteProviderServices, System.Data.SQLite.EF6" />
    </providers>
  </entityFramework>
  
  <system.data>
    <DbProviderFactories>
      <remove invariant="System.Data.SQLite.EF6" />
      <add name="SQLite Data Provider (Entity Framework 6)" invariant="System.Data.SQLite.EF6" description=".NET Framework Data Provider for SQLite (Entity Framework 6)" type="System.Data.SQLite.EF6.SQLiteProviderFactory, System.Data.SQLite.EF6" />
    </DbProviderFactories>
  </system.data>
  
  <appSettings>
    <add key="CompanyName" value="شركة المحاسبة المتقدمة" />
    <add key="Version" value="1.0.0" />
    <add key="Language" value="ar-SA" />
    <add key="Theme" value="Light" />
    <add key="BackupPath" value="Backups\" />
    <add key="ReportsPath" value="Reports\" />
  </appSettings>
</configuration>
