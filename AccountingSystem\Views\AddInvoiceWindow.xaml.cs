using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class AddInvoiceWindow : Window
    {
        public ObservableCollection<InvoiceItemDetail> InvoiceItems { get; set; }
        private string invoiceType;

        public AddInvoiceWindow(string type)
        {
            InitializeComponent();
            invoiceType = type;
            InitializeWindow();
            InitializeData();
            DataContext = this;
        }

        private void InitializeWindow()
        {
            if (invoiceType == "مشتريات")
            {
                HeaderTitle.Text = "إضافة فاتورة مشتريات جديدة";
                HeaderIcon.Text = "🛒";
                HeaderBorder.Background = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange
                CustomerSupplierLabel.Text = "المورد *";
                
                // Update supplier list for purchases
                CustomerSupplierComboBox.Items.Clear();
                CustomerSupplierComboBox.Items.Add(new ComboBoxItem { Content = "مؤسسة النور للتوريدات" });
                CustomerSupplierComboBox.Items.Add(new ComboBoxItem { Content = "مصنع الجودة" });
                CustomerSupplierComboBox.Items.Add(new ComboBoxItem { Content = "شركة التقنية المتطورة" });
                CustomerSupplierComboBox.Items.Add(new ComboBoxItem { Content = "مؤسسة الإمداد" });
            }
        }

        private void InitializeData()
        {
            // Initialize invoice items
            InvoiceItems = new ObservableCollection<InvoiceItemDetail>();
            
            // Generate invoice number
            GenerateInvoiceNumber();
            
            // Set current date
            InvoiceDateTextBox.Text = DateTime.Now.ToString("yyyy-MM-dd");
            
            // Set up data grid
            InvoiceItemsDataGrid.ItemsSource = InvoiceItems;
            
            // Add initial empty items
            AddEmptyItem();
            AddEmptyItem();
            
            // Calculate initial totals
            CalculateTotals();
        }

        private void GenerateInvoiceNumber()
        {
            var random = new Random();
            var prefix = invoiceType == "مبيعات" ? "SAL" : "PUR";
            var invoiceNumber = $"{prefix}-{DateTime.Now:yyyyMM}-{random.Next(100, 999)}";
            InvoiceNumberTextBox.Text = invoiceNumber;
        }

        private void AddItemButton_Click(object sender, RoutedEventArgs e)
        {
            AddEmptyItem();
        }

        private void ScanBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            var barcodeInput = Microsoft.VisualBasic.Interaction.InputBox(
                "امسح الباركود أو اكتبه:",
                "مسح الباركود",
                "");

            if (!string.IsNullOrEmpty(barcodeInput))
            {
                AddItemByBarcode(barcodeInput.Trim());
            }
        }

        private void SelectFromInventoryButton_Click(object sender, RoutedEventArgs e)
        {
            var itemsWindow = new ItemsWindow();
            itemsWindow.ShowDialog();
            MessageBox.Show("سيتم ربط نافذة الأصناف لاختيار الصنف قريباً", "اختيار من المخزون", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RemoveItemButton_Click(object sender, RoutedEventArgs e)
        {
            if (InvoiceItemsDataGrid.SelectedItem is InvoiceItemDetail selectedItem)
            {
                InvoiceItems.Remove(selectedItem);
                CalculateTotals();
            }
            else
            {
                MessageBox.Show("يرجى اختيار الصنف المراد حذفه", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void AddItemByBarcode(string barcode)
        {
            // Sample items data for barcode lookup
            var sampleItems = new Dictionary<string, (string name, string unit, decimal price)>
            {
                { "1234567890123", ("لابتوب ديل XPS 13", "قطعة", 4200.00m) },
                { "2345678901234", ("قميص قطني أزرق", "قطعة", 75.00m) },
                { "3456789012345", ("أرز بسمتي 5 كيلو", "كيس", 35.00m) },
                { "4567890123456", ("قلم حبر جاف أزرق", "قطعة", 5.00m) },
                { "5678901234567", ("مكنسة كهربائية", "قطعة", 250.00m) }
            };

            if (sampleItems.ContainsKey(barcode))
            {
                var item = sampleItems[barcode];
                var newItem = new InvoiceItemDetail
                {
                    Barcode = barcode,
                    ItemName = item.name,
                    Unit = item.unit,
                    Quantity = "1",
                    UnitPrice = item.price.ToString("F2"),
                    Total = item.price.ToString("F2")
                };

                InvoiceItems.Add(newItem);
                CalculateTotals();

                MessageBox.Show($"تم إضافة الصنف:\n{item.name}\nالسعر: {item.price:F2} ر.س",
                               "تم إضافة الصنف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("لم يتم العثور على صنف بهذا الباركود", "باركود غير موجود", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void AddEmptyItem()
        {
            var newItem = new InvoiceItemDetail();
            InvoiceItems.Add(newItem);
        }

        private void CalculateTotals()
        {
            decimal subtotal = 0;

            foreach (var item in InvoiceItems)
            {
                if (decimal.TryParse(item.Quantity, out decimal qty) && 
                    decimal.TryParse(item.UnitPrice, out decimal price))
                {
                    decimal itemTotal = qty * price;
                    item.Total = itemTotal.ToString("N2");
                    subtotal += itemTotal;
                }
            }

            SubtotalLabel.Text = subtotal.ToString("N2");
            
            // Calculate discount
            decimal discount = 0;
            if (decimal.TryParse(DiscountTextBox.Text, out discount))
            {
                subtotal -= discount;
            }
            
            // Calculate tax (15%)
            decimal tax = subtotal * 0.15m;
            TaxLabel.Text = tax.ToString("N2");
            
            // Calculate final total
            decimal total = subtotal + tax;
            TotalLabel.Text = total.ToString("N2");
        }

        private void SaveDraftButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInvoice(false))
            {
                var message = $"تم حفظ الفاتورة كمسودة بنجاح!\n\n" +
                             $"رقم الفاتورة: {InvoiceNumberTextBox.Text}\n" +
                             $"النوع: {invoiceType}\n" +
                             $"التاريخ: {InvoiceDateTextBox.Text}\n" +
                             $"الإجمالي: {TotalLabel.Text} ر.س";

                MessageBox.Show(message, "تم الحفظ كمسودة", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
                this.Close();
            }
        }

        private void SaveAndSendButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInvoice(true))
            {
                var message = $"تم حفظ وإرسال الفاتورة بنجاح!\n\n" +
                             $"رقم الفاتورة: {InvoiceNumberTextBox.Text}\n" +
                             $"النوع: {invoiceType}\n" +
                             $"التاريخ: {InvoiceDateTextBox.Text}\n" +
                             $"العميل/المورد: {((ComboBoxItem)CustomerSupplierComboBox.SelectedItem)?.Content}\n" +
                             $"الإجمالي النهائي: {TotalLabel.Text} ر.س";

                MessageBox.Show(message, "تم الحفظ والإرسال", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
                this.Close();
            }
        }

        private void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInvoice(false))
            {
                MessageBox.Show("سيتم فتح معاينة الفاتورة قريباً", "معاينة الفاتورة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء العملية؟ سيتم فقدان البيانات المدخلة.", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result == MessageBoxResult.Yes)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        private bool ValidateInvoice(bool requireComplete)
        {
            if (CustomerSupplierComboBox.SelectedItem == null)
            {
                var label = invoiceType == "مبيعات" ? "العميل" : "المورد";
                MessageBox.Show($"يرجى اختيار {label}", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CustomerSupplierComboBox.Focus();
                return false;
            }

            var validItems = InvoiceItems.Where(i => !string.IsNullOrEmpty(i.ItemName)).ToList();
            if (validItems.Count == 0)
            {
                MessageBox.Show("يجب إضافة صنف واحد على الأقل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (requireComplete)
            {
                foreach (var item in validItems)
                {
                    if (string.IsNullOrEmpty(item.Quantity) || string.IsNullOrEmpty(item.UnitPrice))
                    {
                        MessageBox.Show("يرجى إكمال بيانات جميع الأصناف (الكمية والسعر)", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                        return false;
                    }
                }
            }

            return true;
        }
    }

    public class InvoiceItemDetail
    {
        public string Barcode { get; set; }
        public string ItemName { get; set; }
        public string Unit { get; set; }
        public string Quantity { get; set; }
        public string UnitPrice { get; set; }
        public string Total { get; set; }
    }
}
