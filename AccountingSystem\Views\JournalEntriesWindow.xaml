<Window x:Class="AccountingSystem.Views.JournalEntriesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="القيود اليومية - نظام المحاسبة المتقدم" 
        Height="650" Width="1000"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#673AB7">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="📚" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="القيود اليومية" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="➕ قيد جديد" 
                            Background="#4CAF50" Foreground="White"
                            Width="120" Height="35" Margin="5,0"
                            Click="AddJournalEntryButton_Click"/>
                    <Button Content="إغلاق" 
                            Background="#F44336" Foreground="White"
                            Width="80" Height="35" Margin="5,0"
                            Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Filter and Search -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="من تاريخ:" FontSize="12" Margin="0,0,0,5"/>
                    <TextBox x:Name="FromDateTextBox" Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" Text="01/01/2024"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="إلى تاريخ:" FontSize="12" Margin="0,0,0,5"/>
                    <TextBox x:Name="ToDateTextBox" Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" Text="31/12/2024"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="البحث:" FontSize="12" Margin="0,0,0,5"/>
                    <TextBox x:Name="SearchTextBox" Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" Text="البحث في القيود..." 
                             Foreground="#999"
                             GotFocus="SearchTextBox_GotFocus"
                             LostFocus="SearchTextBox_LostFocus"/>
                </StackPanel>
                
                <Button Grid.Column="3" Content="🔍 بحث"
                        Height="35" FontSize="14" Margin="10,25,0,0"
                        Background="#673AB7" Foreground="White"
                        Click="SearchButton_Click"/>
            </Grid>
            
            <!-- Journal Entries List -->
            <Border Grid.Row="1" BorderBrush="#DDD" BorderThickness="1" CornerRadius="5">
                <DataGrid x:Name="JournalEntriesDataGrid" 
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          FontSize="13">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم القيد" Binding="{Binding EntryNumber}" Width="100"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date}" Width="120"/>
                        <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                        <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount}" Width="120"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="100"/>
                        <DataGridTemplateColumn Header="الإجراءات" Width="180">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="👁️" Width="30" Height="25" Margin="2"
                                                Background="#2196F3" Foreground="White"
                                                Click="ViewEntryButton_Click"
                                                ToolTip="عرض"/>
                                        <Button Content="✏️" Width="30" Height="25" Margin="2"
                                                Background="#FF9800" Foreground="White"
                                                Click="EditEntryButton_Click"
                                                ToolTip="تعديل"/>
                                        <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                                Background="#F44336" Foreground="White"
                                                Click="DeleteEntryButton_Click"
                                                ToolTip="حذف"/>
                                        <Button Content="📄" Width="30" Height="25" Margin="2"
                                                Background="#4CAF50" Foreground="White"
                                                Click="PrintEntryButton_Click"
                                                ToolTip="طباعة"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>
        </Grid>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="StatusLabel"
                           Text="جاهز - 0 قيد"
                           VerticalAlignment="Center" Margin="10,0"
                           FontSize="12" Foreground="#666"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <TextBlock Text="إجمالي المبلغ:" VerticalAlignment="Center" Margin="0,0,10,0" FontSize="12"/>
                    <TextBlock x:Name="TotalAmountLabel" Text="0.00 ر.س" 
                               VerticalAlignment="Center" FontWeight="Bold" 
                               Foreground="#673AB7" Margin="0,0,20,0"/>
                    <Button Content="📤 تصدير" Width="80" Height="30" Margin="5,0"
                            Background="#607D8B" Foreground="White"
                            Click="ExportButton_Click"/>
                    <Button Content="🔄 تحديث" Width="80" Height="30" Margin="5,0"
                            Background="#9C27B0" Foreground="White"
                            Click="RefreshButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
