using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class SettingsWindow : Window
    {
        public SettingsWindow()
        {
            try
            {
                InitializeComponent();
                LoadCurrentSettings();
                UpdatePreviews();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل نافذة الإعدادات: " + ex.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCurrentSettings()
        {
            // Load saved settings or use defaults
            // This would normally load from a settings file or database
        }

        private void UpdatePreviews()
        {
            try
            {
                UpdateLanguagePreview();
                UpdateCurrencyPreview();
                UpdateThemePreview();
            }
            catch
            {
                // Ignore preview errors
            }
        }

        private void UpdateLanguagePreview()
        {
            try
            {
                var dateFormat = ((ComboBoxItem)DateFormatComboBox.SelectedItem)?.Content.ToString() ?? "dd/MM/yyyy";
                var timeFormat = ((ComboBoxItem)TimeFormatComboBox.SelectedItem)?.Content.ToString() ?? "24 ساعة (HH:mm)";
                var numberType = ((ComboBoxItem)NumberTypeComboBox.SelectedItem)?.Content.ToString() ?? "أرقام هندية";

                var sampleDate = DateTime.Now.ToString("dd/MM/yyyy");
                var sampleTime = DateTime.Now.ToString("HH:mm");
                var sampleAmount = numberType.Contains("عربية") ? "١٬٢٣٤٫٥٦" : "1,234.56";

                PreviewTextBlock.Text = "التاريخ: " + sampleDate + " - الوقت: " + sampleTime + " - المبلغ: " + sampleAmount + " ر.س";
            }
            catch
            {
                // Ignore errors during preview update
            }
        }

        private void UpdateCurrencyPreview()
        {
            try
            {
                var symbol = CurrencySymbolTextBox.Text;
                var position = ((ComboBoxItem)CurrencyPositionComboBox.SelectedItem)?.Content.ToString() ?? "";
                var decimals = int.Parse(((ComboBoxItem)DecimalPlacesComboBox.SelectedItem)?.Content.ToString() ?? "2");

                var amount = 1234.56m;
                var formattedAmount = amount.ToString("N" + decimals.ToString());

                if (position.Contains("قبل"))
                {
                    CurrencyPreviewTextBlock.Text = "المبلغ: " + symbol + " " + formattedAmount;
                }
                else
                {
                    CurrencyPreviewTextBlock.Text = "المبلغ: " + formattedAmount + " " + symbol;
                }
            }
            catch
            {
                // Ignore errors during preview update
            }
        }

        private void UpdateThemePreview()
        {
            var primaryColor = PrimaryColorButton.Background;
            ThemePreviewBorder.BorderBrush = primaryColor;
        }

        // Navigation Methods
        private void CompanyInfoButton_Click(object sender, RoutedEventArgs e)
        {
            ShowPanel("CompanyInfo");
            UpdateButtonStyles(CompanyInfoButton);
        }

        private void LanguageButton_Click(object sender, RoutedEventArgs e)
        {
            ShowPanel("Language");
            UpdateButtonStyles(LanguageButton);
        }

        private void CurrencyButton_Click(object sender, RoutedEventArgs e)
        {
            ShowPanel("Currency");
            UpdateButtonStyles(CurrencyButton);
        }

        private void ThemeButton_Click(object sender, RoutedEventArgs e)
        {
            ShowPanel("Theme");
            UpdateButtonStyles(ThemeButton);
        }

        private void PrintingButton_Click(object sender, RoutedEventArgs e)
        {
            ShowPanel("Printing");
            UpdateButtonStyles(PrintingButton);
        }

        private void BackupButton_Click(object sender, RoutedEventArgs e)
        {
            ShowPanel("Backup");
            UpdateButtonStyles(BackupButton);
        }

        private void SecurityButton_Click(object sender, RoutedEventArgs e)
        {
            ShowPanel("Security");
            UpdateButtonStyles(SecurityButton);
        }

        private void SystemButton_Click(object sender, RoutedEventArgs e)
        {
            ShowPanel("System");
            UpdateButtonStyles(SystemButton);
        }

        private void ShowPanel(string panelName)
        {
            // Hide all panels
            CompanyInfoPanel.Visibility = Visibility.Collapsed;
            LanguagePanel.Visibility = Visibility.Collapsed;
            CurrencyPanel.Visibility = Visibility.Collapsed;
            ThemePanel.Visibility = Visibility.Collapsed;
            PrintingPanel.Visibility = Visibility.Collapsed;
            BackupPanel.Visibility = Visibility.Collapsed;
            SecurityPanel.Visibility = Visibility.Collapsed;
            SystemPanel.Visibility = Visibility.Collapsed;

            // Show selected panel
            switch (panelName)
            {
                case "CompanyInfo":
                    CompanyInfoPanel.Visibility = Visibility.Visible;
                    break;
                case "Language":
                    LanguagePanel.Visibility = Visibility.Visible;
                    break;
                case "Currency":
                    CurrencyPanel.Visibility = Visibility.Visible;
                    break;
                case "Theme":
                    ThemePanel.Visibility = Visibility.Visible;
                    break;
                case "Printing":
                    PrintingPanel.Visibility = Visibility.Visible;
                    break;
                case "Backup":
                    BackupPanel.Visibility = Visibility.Visible;
                    break;
                case "Security":
                    SecurityPanel.Visibility = Visibility.Visible;
                    break;
                case "System":
                    SystemPanel.Visibility = Visibility.Visible;
                    break;
            }
        }

        private void UpdateButtonStyles(Button selectedButton)
        {
            // Reset all button styles
            CompanyInfoButton.Background = Brushes.White;
            LanguageButton.Background = Brushes.White;
            CurrencyButton.Background = Brushes.White;
            ThemeButton.Background = Brushes.White;
            PrintingButton.Background = Brushes.White;
            BackupButton.Background = Brushes.White;
            SecurityButton.Background = Brushes.White;
            SystemButton.Background = Brushes.White;

            // Highlight selected button
            selectedButton.Background = new SolidColorBrush(Color.FromRgb(227, 242, 253));
        }

        // Company Info Methods
        private void SelectLogoButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة اختيار الشعار قريباً", "اختيار شعار", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RemoveLogoButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد حذف شعار الشركة؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("تم حذف الشعار", "حذف الشعار", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        // Currency Methods
        private void AddCurrencyButton_Click(object sender, RoutedEventArgs e)
        {
            var addCurrencyWindow = new AddCurrencyWindow();
            if (addCurrencyWindow.ShowDialog() == true)
            {
                // Refresh currency list
                MessageBox.Show("تم إضافة العملة بنجاح", "إضافة عملة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void RemoveCurrencyButton_Click(object sender, RoutedEventArgs e)
        {
            if (SupportedCurrenciesListBox.SelectedItem != null)
            {
                var selectedCurrency = ((ListBoxItem)SupportedCurrenciesListBox.SelectedItem).Content.ToString();
                var result = MessageBox.Show($"هل تريد حذف العملة: {selectedCurrency}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                if (result == MessageBoxResult.Yes)
                {
                    SupportedCurrenciesListBox.Items.Remove(SupportedCurrenciesListBox.SelectedItem);
                    MessageBox.Show("تم حذف العملة", "حذف عملة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار عملة للحذف", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void EditCurrencyButton_Click(object sender, RoutedEventArgs e)
        {
            if (SupportedCurrenciesListBox.SelectedItem != null)
            {
                var selectedCurrency = ((ListBoxItem)SupportedCurrenciesListBox.SelectedItem).Content.ToString();
                var newName = Microsoft.VisualBasic.Interaction.InputBox("تعديل اسم العملة:", "تعديل عملة", selectedCurrency);
                if (!string.IsNullOrEmpty(newName))
                {
                    ((ListBoxItem)SupportedCurrenciesListBox.SelectedItem).Content = newName;
                    MessageBox.Show("تم تعديل العملة", "تعديل عملة", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار عملة للتعديل", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        // Theme Methods
        private void SelectPrimaryColorButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح منتقي الألوان قريباً", "اختيار لون", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CustomPrimaryColorButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح منتقي الألوان المخصص قريباً", "لون مخصص", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SelectSecondaryColorButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح منتقي الألوان قريباً", "اختيار لون", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CustomSecondaryColorButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح منتقي الألوان المخصص قريباً", "لون مخصص", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void QuickColor_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var color = button.Tag.ToString();
            PrimaryColorButton.Background = new SolidColorBrush((Color)ColorConverter.ConvertFromString(color));
            UpdateThemePreview();
            MessageBox.Show("تم تطبيق اللون: " + color, "تغيير اللون", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        // Main Action Methods
        private void SaveSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // Save all settings
            var message = "تم حفظ الإعدادات بنجاح!\n\n" +
                         "اسم الشركة: " + CompanyNameTextBox.Text + "\n" +
                         "اللغة: " + (((ComboBoxItem)LanguageComboBox.SelectedItem)?.Content ?? "العربية") + "\n" +
                         "العملة: " + (((ComboBoxItem)BaseCurrencyComboBox.SelectedItem)?.Content ?? "ريال سعودي") + "\n" +
                         "المظهر: " + (((ComboBoxItem)ThemeComboBox.SelectedItem)?.Content ?? "فاتح");

            MessageBox.Show(message, "حفظ الإعدادات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RestoreDefaultsButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع التخصيصات.", "استعادة الافتراضي", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result == MessageBoxResult.Yes)
            {
                LoadDefaultSettings();
                MessageBox.Show("تم استعادة الإعدادات الافتراضية", "استعادة الافتراضي", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير الإعدادات إلى ملف قريباً", "تصدير الإعدادات", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        // Event Handlers for Live Preview Updates
        private void LanguageComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            UpdateLanguagePreview();
        }

        private void NumberTypeComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            UpdateLanguagePreview();
        }

        private void CurrencySymbolTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            UpdateCurrencyPreview();
        }

        private void CurrencyPositionComboBox_SelectionChanged(object sender, System.Windows.Controls.SelectionChangedEventArgs e)
        {
            UpdateCurrencyPreview();
        }

        private void LoadDefaultSettings()
        {
            // Reset to default values
            CompanyNameTextBox.Text = "شركة المحاسبة المتقدمة";
            LanguageComboBox.SelectedIndex = 0;
            NumberTypeComboBox.SelectedIndex = 1;
            BaseCurrencyComboBox.SelectedIndex = 0;
            CurrencySymbolTextBox.Text = "ر.س";
            ThemeComboBox.SelectedIndex = 0;
            PrimaryColorButton.Background = new SolidColorBrush(Color.FromRgb(33, 150, 243));

            UpdatePreviews();
        }
    }
}
