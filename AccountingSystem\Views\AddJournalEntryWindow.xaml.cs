using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class AddJournalEntryWindow : Window
    {
        public ObservableCollection<AccountItem> Accounts { get; set; }
        public ObservableCollection<JournalLineItem> JournalLines { get; set; }

        public AddJournalEntryWindow()
        {
            InitializeComponent();
            InitializeData();
            DataContext = this;
        }

        private void InitializeData()
        {
            // Initialize accounts list
            Accounts = new ObservableCollection<AccountItem>
            {
                new AccountItem { AccountNumber = "1001", AccountName = "النقدية", DisplayName = "1001 - النقدية" },
                new AccountItem { AccountNumber = "1002", AccountName = "البنك الأهلي", DisplayName = "1002 - البنك الأهلي" },
                new AccountItem { AccountNumber = "1003", AccountName = "العملاء", DisplayName = "1003 - العملاء" },
                new AccountItem { AccountNumber = "2001", AccountName = "الموردون", DisplayName = "2001 - الموردون" },
                new AccountItem { AccountNumber = "3001", AccountName = "رأس المال", DisplayName = "3001 - رأس المال" },
                new AccountItem { AccountNumber = "4001", AccountName = "مبيعات", DisplayName = "4001 - مبيعات" },
                new AccountItem { AccountNumber = "5001", AccountName = "تكلفة البضاعة المباعة", DisplayName = "5001 - تكلفة البضاعة المباعة" },
                new AccountItem { AccountNumber = "5002", AccountName = "مصروفات إدارية", DisplayName = "5002 - مصروفات إدارية" }
            };

            // Initialize journal lines
            JournalLines = new ObservableCollection<JournalLineItem>();
            
            // Generate entry number
            GenerateEntryNumber();
            
            // Set current date
            EntryDateTextBox.Text = DateTime.Now.ToString("yyyy-MM-dd");
            
            // Set up data grid
            JournalLinesDataGrid.ItemsSource = JournalLines;
            
            // Add initial empty lines
            AddEmptyLine();
            AddEmptyLine();
        }

        private void GenerateEntryNumber()
        {
            var random = new Random();
            var entryNumber = $"JE{DateTime.Now:yyyyMM}{random.Next(100, 999)}";
            EntryNumberTextBox.Text = entryNumber;
        }

        private void AddLineButton_Click(object sender, RoutedEventArgs e)
        {
            AddEmptyLine();
        }

        private void RemoveLineButton_Click(object sender, RoutedEventArgs e)
        {
            if (JournalLinesDataGrid.SelectedItem is JournalLineItem selectedLine)
            {
                JournalLines.Remove(selectedLine);
                CalculateTotals();
            }
            else
            {
                MessageBox.Show("يرجى اختيار السطر المراد حذفه", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void AddEmptyLine()
        {
            var newLine = new JournalLineItem();
            JournalLines.Add(newLine);
        }

        private void CalculateTotals()
        {
            decimal totalDebit = 0;
            decimal totalCredit = 0;

            foreach (var line in JournalLines)
            {
                if (decimal.TryParse(line.DebitAmount, out decimal debit))
                    totalDebit += debit;
                
                if (decimal.TryParse(line.CreditAmount, out decimal credit))
                    totalCredit += credit;
            }

            TotalDebitLabel.Text = totalDebit.ToString("N2");
            TotalCreditLabel.Text = totalCredit.ToString("N2");
            
            decimal difference = totalDebit - totalCredit;
            DifferenceLabel.Text = Math.Abs(difference).ToString("N2");
            
            // Change color based on balance
            if (difference == 0)
            {
                DifferenceLabel.Foreground = Brushes.Green;
            }
            else
            {
                DifferenceLabel.Foreground = Brushes.Red;
            }
        }

        private void SaveDraftButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateEntry(false))
            {
                var message = $"تم حفظ القيد كمسودة بنجاح!\n\n" +
                             $"رقم القيد: {EntryNumberTextBox.Text}\n" +
                             $"التاريخ: {EntryDateTextBox.Text}\n" +
                             $"الوصف: {DescriptionTextBox.Text}\n" +
                             $"عدد الأسطر: {JournalLines.Count(l => !string.IsNullOrEmpty(l.AccountNumber))}";

                MessageBox.Show(message, "تم الحفظ كمسودة", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
                this.Close();
            }
        }

        private void SaveAndPostButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateEntry(true))
            {
                var message = $"تم حفظ وترحيل القيد بنجاح!\n\n" +
                             $"رقم القيد: {EntryNumberTextBox.Text}\n" +
                             $"التاريخ: {EntryDateTextBox.Text}\n" +
                             $"الوصف: {DescriptionTextBox.Text}\n" +
                             $"إجمالي المدين: {TotalDebitLabel.Text}\n" +
                             $"إجمالي الدائن: {TotalCreditLabel.Text}";

                MessageBox.Show(message, "تم الحفظ والترحيل", MessageBoxButton.OK, MessageBoxImage.Information);
                this.DialogResult = true;
                this.Close();
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد مسح جميع البيانات؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                ClearAllFields();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء العملية؟ سيتم فقدان البيانات المدخلة.", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result == MessageBoxResult.Yes)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        private bool ValidateEntry(bool requireBalance)
        {
            if (string.IsNullOrWhiteSpace(DescriptionTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال وصف القيد", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                DescriptionTextBox.Focus();
                return false;
            }

            var validLines = JournalLines.Where(l => !string.IsNullOrEmpty(l.AccountNumber)).ToList();
            if (validLines.Count < 2)
            {
                MessageBox.Show("يجب أن يحتوي القيد على سطرين على الأقل", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (requireBalance)
            {
                CalculateTotals();
                if (TotalDebitLabel.Text != TotalCreditLabel.Text)
                {
                    MessageBox.Show("يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن", "خطأ في الميزان", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }
            }

            return true;
        }

        private void ClearAllFields()
        {
            GenerateEntryNumber();
            EntryDateTextBox.Text = DateTime.Now.ToString("yyyy-MM-dd");
            ReferenceTextBox.Clear();
            DescriptionTextBox.Clear();
            JournalLines.Clear();
            AddEmptyLine();
            AddEmptyLine();
            CalculateTotals();
        }
    }

    public class JournalLineItem
    {
        public string AccountNumber { get; set; }
        public string Description { get; set; }
        public string DebitAmount { get; set; }
        public string CreditAmount { get; set; }
    }
}
