using System;
using System.Globalization;
using System.Windows;

namespace AccountingSystem.Views
{
    public partial class BasicSettingsWindow : Window
    {
        public BasicSettingsWindow()
        {
            InitializeComponent();
            LoadCurrentSettings();
        }

        private void LoadCurrentSettings()
        {
            try
            {
                var culture = CultureInfo.CurrentCulture;
                var now = DateTime.Now;
                var sampleAmount = 1234.56m;

                var formatText = "🌍 المنطقة: " + culture.DisplayName + "\r\n" +
                               "📅 التاريخ: " + now.ToString("d", culture) + "\r\n" +
                               "🕐 الوقت: " + now.ToString("t", culture) + "\r\n" +
                               "💰 العملة: " + sampleAmount.ToString("C", culture) + "\r\n" +
                               "🔢 الأرقام: " + sampleAmount.ToString("N2", culture) + "\r\n" +
                               "📊 النسبة: " + (15.5m).ToString("N2", culture) + "%\r\n\r\n" +
                               "رمز العملة: " + culture.NumberFormat.CurrencySymbol + "\r\n" +
                               "فاصل الآلاف: '" + culture.NumberFormat.NumberGroupSeparator + "'\r\n" +
                               "الفاصلة العشرية: '" + culture.NumberFormat.NumberDecimalSeparator + "'";

                FormatInfoTextBox.Text = formatText;
            }
            catch (Exception ex)
            {
                FormatInfoTextBox.Text = "خطأ في تحميل معلومات التنسيق: " + ex.Message;
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var message = "تم حفظ الإعدادات بنجاح!\r\n\r\n" +
                             "معلومات الشركة:\r\n" +
                             "• الاسم: " + CompanyNameTextBox.Text + "\r\n" +
                             "• الرقم الضريبي: " + TaxNumberTextBox.Text + "\r\n" +
                             "• الهاتف: " + PhoneTextBox.Text + "\r\n\r\n" +
                             "ملاحظة: إعدادات التنسيق تتبع نظام ويندوز تلقائياً.";

                MessageBox.Show(message, "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حفظ الإعدادات: " + ex.Message, "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void WindowsSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start("ms-settings:regionlanguage");
            }
            catch
            {
                try
                {
                    System.Diagnostics.Process.Start("intl.cpl");
                }
                catch
                {
                    MessageBox.Show(
                        "لا يمكن فتح إعدادات ويندوز تلقائياً.\r\n\r\n" +
                        "يرجى فتح إعدادات ويندوز يدوياً:\r\n" +
                        "1. اضغط على زر ابدأ\r\n" +
                        "2. اختر الإعدادات\r\n" +
                        "3. اذهب إلى الوقت واللغة\r\n" +
                        "4. اختر المنطقة",
                        "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
