<Window x:Class="AccountingSystem.Views.SimpleSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات النظام - نظام المحاسبة المتقدم" 
        Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#3F51B5">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="⚙️" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إعدادات النظام" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="30,20">
            <StackPanel>
                
                <!-- Company Information -->
                <TextBlock Text="معلومات الشركة" FontSize="18" FontWeight="Bold" Margin="0,0,0,15" Foreground="#3F51B5"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                        <TextBlock Text="اسم الشركة/المؤسسة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CompanyNameTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Margin="0,0,0,15"
                                 Text="شركة المحاسبة المتقدمة"/>
                        
                        <TextBlock Text="الرقم الضريبي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TaxNumberTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Margin="0,0,0,15"
                                 Text="300123456789003"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="15,0,0,0">
                        <TextBlock Text="رقم الهاتف" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PhoneTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Margin="0,0,0,15"
                                 Text="+966 11 123 4567"/>
                        
                        <TextBlock Text="البريد الإلكتروني" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="EmailTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Margin="0,0,0,15"
                                 Text="<EMAIL>"/>
                    </StackPanel>
                </Grid>
                
                <!-- Language and Numbers -->
                <TextBlock Text="اللغة والأرقام" FontSize="18" FontWeight="Bold" Margin="0,0,0,15" Foreground="#3F51B5"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                        <TextBlock Text="لغة الواجهة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="LanguageComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9" Margin="0,0,0,15">
                            <ComboBoxItem Content="العربية" IsSelected="True"/>
                            <ComboBoxItem Content="English"/>
                            <ComboBoxItem Content="Français"/>
                        </ComboBox>
                        
                        <TextBlock Text="نوع الأرقام" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="NumberTypeComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9" Margin="0,0,0,15">
                            <ComboBoxItem Content="أرقام عربية (١٢٣٤٥٦٧٨٩٠)"/>
                            <ComboBoxItem Content="أرقام هندية (1234567890)" IsSelected="True"/>
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="15,0,0,0">
                        <TextBlock Text="تنسيق التاريخ" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="DateFormatComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9" Margin="0,0,0,15">
                            <ComboBoxItem Content="dd/MM/yyyy" IsSelected="True"/>
                            <ComboBoxItem Content="MM/dd/yyyy"/>
                            <ComboBoxItem Content="yyyy-MM-dd"/>
                        </ComboBox>
                        
                        <TextBlock Text="تنسيق الوقت" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="TimeFormatComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9" Margin="0,0,0,15">
                            <ComboBoxItem Content="24 ساعة (HH:mm)" IsSelected="True"/>
                            <ComboBoxItem Content="12 ساعة (hh:mm AM/PM)"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>
                
                <!-- Currency Settings -->
                <TextBlock Text="إعدادات العملة" FontSize="18" FontWeight="Bold" Margin="0,0,0,15" Foreground="#3F51B5"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                        <TextBlock Text="العملة الأساسية" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="BaseCurrencyComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9" Margin="0,0,0,15">
                            <ComboBoxItem Content="ريال سعودي (SAR)" IsSelected="True"/>
                            <ComboBoxItem Content="دولار أمريكي (USD)"/>
                            <ComboBoxItem Content="يورو (EUR)"/>
                            <ComboBoxItem Content="جنيه إسترليني (GBP)"/>
                        </ComboBox>
                        
                        <TextBlock Text="رمز العملة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CurrencySymbolTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Margin="0,0,0,15"
                                 Text="ر.س"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="15,0,0,0">
                        <TextBlock Text="موضع رمز العملة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CurrencyPositionComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9" Margin="0,0,0,15">
                            <ComboBoxItem Content="بعد المبلغ (1,234.56 ر.س)" IsSelected="True"/>
                            <ComboBoxItem Content="قبل المبلغ (ر.س 1,234.56)"/>
                        </ComboBox>
                        
                        <TextBlock Text="عدد الخانات العشرية" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="DecimalPlacesComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9" Margin="0,0,0,15">
                            <ComboBoxItem Content="0"/>
                            <ComboBoxItem Content="1"/>
                            <ComboBoxItem Content="2" IsSelected="True"/>
                            <ComboBoxItem Content="3"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>
                
                <!-- Theme Settings -->
                <TextBlock Text="المظهر والألوان" FontSize="18" FontWeight="Bold" Margin="0,0,0,15" Foreground="#3F51B5"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                        <TextBlock Text="المظهر العام" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="ThemeComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9" Margin="0,0,0,15">
                            <ComboBoxItem Content="فاتح (Light)" IsSelected="True"/>
                            <ComboBoxItem Content="داكن (Dark)"/>
                            <ComboBoxItem Content="تلقائي (Auto)"/>
                        </ComboBox>
                        
                        <TextBlock Text="حجم الخط" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="FontSizeComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9" Margin="0,0,0,15">
                            <ComboBoxItem Content="صغير (12px)"/>
                            <ComboBoxItem Content="متوسط (14px)" IsSelected="True"/>
                            <ComboBoxItem Content="كبير (16px)"/>
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="15,0,0,0">
                        <TextBlock Text="اللون الأساسي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                            <Button Width="30" Height="30" Background="#2196F3" Margin="2" Click="ColorButton_Click" Tag="#2196F3"/>
                            <Button Width="30" Height="30" Background="#4CAF50" Margin="2" Click="ColorButton_Click" Tag="#4CAF50"/>
                            <Button Width="30" Height="30" Background="#FF9800" Margin="2" Click="ColorButton_Click" Tag="#FF9800"/>
                            <Button Width="30" Height="30" Background="#F44336" Margin="2" Click="ColorButton_Click" Tag="#F44336"/>
                            <Button Width="30" Height="30" Background="#9C27B0" Margin="2" Click="ColorButton_Click" Tag="#9C27B0"/>
                        </StackPanel>
                        
                        <CheckBox Content="تأثيرات بصرية" IsChecked="True" Margin="0,5"/>
                        <CheckBox Content="ظلال النوافذ" IsChecked="True" Margin="0,5"/>
                        <CheckBox Content="الرسوم المتحركة" IsChecked="False" Margin="0,5"/>
                    </StackPanel>
                </Grid>
                
                <!-- Preview -->
                <Border Background="#F0F0F0" CornerRadius="5" Padding="15" Margin="0,10,0,0">
                    <StackPanel>
                        <TextBlock Text="معاينة الإعدادات" FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>
                        <TextBlock x:Name="PreviewTextBlock" 
                                   Text="التاريخ: 15/01/2025 - الوقت: 14:30 - المبلغ: 1,234.56 ر.س"
                                   FontSize="13" Background="White" Padding="10"/>
                    </StackPanel>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ الإعدادات" 
                        Width="140" Height="40" FontSize="14" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White" Margin="10,0"
                        Click="SaveSettingsButton_Click"/>
                <Button Content="🔄 استعادة الافتراضي" 
                        Width="140" Height="40" FontSize="14"
                        Background="#FF9800" Foreground="White" Margin="10,0"
                        Click="RestoreDefaultsButton_Click"/>
                <Button Content="❌ إغلاق" 
                        Width="140" Height="40" FontSize="14"
                        Background="#F44336" Foreground="White" Margin="10,0"
                        Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
