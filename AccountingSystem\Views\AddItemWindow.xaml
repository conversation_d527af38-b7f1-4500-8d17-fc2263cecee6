<Window x:Class="AccountingSystem.Views.AddItemWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة صنف جديد - نظام المحاسبة المتقدم" 
        Height="650" Width="700"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#9C27B0">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="📦" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إضافة صنف جديد" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="30,20">
                
                <!-- Basic Information -->
                <TextBlock Text="المعلومات الأساسية" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#9C27B0"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="اسم الصنف *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ItemNameTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                        <TextBlock Text="الفئة *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CategoryComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9">
                            <ComboBoxItem Content="إلكترونيات"/>
                            <ComboBoxItem Content="ملابس"/>
                            <ComboBoxItem Content="أغذية"/>
                            <ComboBoxItem Content="مكتبية"/>
                            <ComboBoxItem Content="أدوات منزلية"/>
                            <ComboBoxItem Content="أخرى"/>
                        </ComboBox>
                    </StackPanel>
                </Grid>
                
                <!-- Barcode Section -->
                <TextBlock Text="معلومات الباركود" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#9C27B0"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="150"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="الباركود *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="BarcodeTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"
                                 KeyDown="BarcodeTextBox_KeyDown"/>
                    </StackPanel>
                    
                    <Button Grid.Column="1" Content="🎲 توليد تلقائي" 
                            Height="35" FontSize="12" Margin="5,25,5,0"
                            Background="#FF9800" Foreground="White"
                            Click="GenerateBarcodeButton_Click"/>
                    
                    <Button Grid.Column="2" Content="📷 مسح باركود" 
                            Height="35" FontSize="12" Margin="5,25,0,0"
                            Background="#2196F3" Foreground="White"
                            Click="ScanBarcodeButton_Click"/>
                </Grid>
                
                <!-- Pricing Information -->
                <TextBlock Text="معلومات التسعير" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#9C27B0"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="سعر الشراء *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PurchasePriceTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"
                                 />
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="5,0">
                        <TextBlock Text="سعر البيع *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="SalePriceTextBox"
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2" Margin="10,0,0,0">
                        <TextBlock Text="هامش الربح" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="ProfitMarginTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#E8F5E8" IsReadOnly="True"/>
                    </StackPanel>
                </Grid>
                
                <!-- Stock Information -->
                <TextBlock Text="معلومات المخزون" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#9C27B0"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="الوحدة *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="UnitComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9">
                            <ComboBoxItem Content="قطعة" IsSelected="True"/>
                            <ComboBoxItem Content="كيلو"/>
                            <ComboBoxItem Content="لتر"/>
                            <ComboBoxItem Content="متر"/>
                            <ComboBoxItem Content="علبة"/>
                            <ComboBoxItem Content="كيس"/>
                            <ComboBoxItem Content="زجاجة"/>
                            <ComboBoxItem Content="عبوة"/>
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="5,0">
                        <TextBlock Text="الكمية الابتدائية" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="InitialQuantityTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Text="0"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2" Margin="10,0,0,0">
                        <TextBlock Text="الحد الأدنى *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="MinimumStockTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Text="5"/>
                    </StackPanel>
                </Grid>
                
                <!-- Additional Information -->
                <TextBlock Text="معلومات إضافية" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#9C27B0"/>
                
                <TextBlock Text="الوصف" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox" 
                         Height="80" FontSize="14" Padding="10"
                         Background="#F9F9F9" Margin="0,0,0,15"
                         TextWrapping="Wrap" AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <CheckBox x:Name="IsActiveCheckBox" 
                                  Content="الصنف نشط" 
                                  FontSize="14" 
                                  IsChecked="True"
                                  Margin="0,10,0,0"/>
                        
                        <CheckBox x:Name="TrackInventoryCheckBox" 
                                  Content="تتبع المخزون" 
                                  FontSize="14" 
                                  IsChecked="True"
                                  Margin="0,5,0,0"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="20,0,0,0">
                        <CheckBox x:Name="AllowNegativeStockCheckBox" 
                                  Content="السماح بالمخزون السالب" 
                                  FontSize="14" 
                                  Margin="0,10,0,0"/>
                        
                        <CheckBox x:Name="PrintBarcodeCheckBox" 
                                  Content="طباعة باركود بعد الحفظ" 
                                  FontSize="14" 
                                  Margin="0,5,0,0"/>
                    </StackPanel>
                </Grid>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ" 
                        Width="120" Height="40" FontSize="14" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White" Margin="10,0"
                        Click="SaveButton_Click"/>
                <Button Content="💾 حفظ وإضافة آخر" 
                        Width="140" Height="40" FontSize="14"
                        Background="#2196F3" Foreground="White" Margin="10,0"
                        Click="SaveAndAddButton_Click"/>
                <Button Content="🔄 مسح الحقول" 
                        Width="120" Height="40" FontSize="14"
                        Background="#FF9800" Foreground="White" Margin="10,0"
                        Click="ClearButton_Click"/>
                <Button Content="❌ إلغاء" 
                        Width="120" Height="40" FontSize="14"
                        Background="#F44336" Foreground="White" Margin="10,0"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
