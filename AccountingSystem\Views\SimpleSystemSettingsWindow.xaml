<Window x:Class="AccountingSystem.Views.SimpleSystemSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات النظام - نظام المحاسبة" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="⚙️" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إعدادات النظام" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <Button Content="✕" 
                        HorizontalAlignment="Left" VerticalAlignment="Center"
                        Width="30" Height="30" Margin="20,0"
                        Background="Transparent" Foreground="White"
                        BorderThickness="0" FontSize="16"
                        Click="CloseButton_Click"/>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
            <StackPanel>
                
                <!-- Company Information -->
                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1"
                        CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="🏢 معلومات الشركة" FontSize="18" FontWeight="Bold" 
                                   Foreground="#495057" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <StackPanel Grid.Row="0" Grid.Column="0">
                                <TextBlock Text="اسم الشركة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="CompanyNameTextBox" Height="35" Padding="10"
                                         Text="شركة المحاسبة المتقدمة" FontSize="14"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2">
                                <TextBlock Text="الرقم الضريبي" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="TaxNumberTextBox" Height="35" Padding="10"
                                         Text="300123456789003" FontSize="14"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="2" Grid.Column="0">
                                <TextBlock Text="رقم الهاتف" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="PhoneTextBox" Height="35" Padding="10"
                                         Text="+966 11 123 4567" FontSize="14"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="2" Grid.Column="2">
                                <TextBlock Text="البريد الإلكتروني" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="EmailTextBox" Height="35" Padding="10"
                                         Text="<EMAIL>" FontSize="14"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="3">
                                <TextBlock Text="العنوان" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox x:Name="AddressTextBox" Height="60" Padding="10"
                                         Text="الرياض، المملكة العربية السعودية" 
                                         FontSize="14" TextWrapping="Wrap" AcceptsReturn="True"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- System Preferences -->
                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1"
                        CornerRadius="8" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="🔧 تفضيلات النظام" FontSize="18" FontWeight="Bold" 
                                   Foreground="#495057" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <StackPanel Grid.Row="0" Grid.Column="0">
                                <TextBlock Text="لغة النظام" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="LanguageComboBox" Height="35" FontSize="14" Padding="10">
                                    <ComboBoxItem Content="العربية" IsSelected="True"/>
                                    <ComboBoxItem Content="English"/>
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="0" Grid.Column="2">
                                <TextBlock Text="المظهر" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="ThemeComboBox" Height="35" FontSize="14" Padding="10">
                                    <ComboBoxItem Content="فاتح" IsSelected="True"/>
                                    <ComboBoxItem Content="داكن"/>
                                    <ComboBoxItem Content="تلقائي"/>
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="2" Grid.Column="0">
                                <TextBlock Text="حجم الخط" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="FontSizeComboBox" Height="35" FontSize="14" Padding="10">
                                    <ComboBoxItem Content="صغير (12px)"/>
                                    <ComboBoxItem Content="متوسط (14px)" IsSelected="True"/>
                                    <ComboBoxItem Content="كبير (16px)"/>
                                    <ComboBoxItem Content="كبير جداً (18px)"/>
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="2" Grid.Column="2">
                                <TextBlock Text="العملة الافتراضية" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox x:Name="CurrencyComboBox" Height="35" FontSize="14" Padding="10">
                                    <ComboBoxItem Content="ريال سعودي (SAR)" IsSelected="True"/>
                                    <ComboBoxItem Content="دولار أمريكي (USD)"/>
                                    <ComboBoxItem Content="يورو (EUR)"/>
                                </ComboBox>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="3">
                                <CheckBox x:Name="AutoBackupCheckBox" Content="تفعيل النسخ الاحتياطي التلقائي" 
                                          FontSize="14" IsChecked="True" Margin="0,10,0,0"/>
                                <CheckBox x:Name="ShowNotificationsCheckBox" Content="إظهار الإشعارات" 
                                          FontSize="14" IsChecked="True" Margin="0,5,0,0"/>
                                <CheckBox x:Name="RememberLoginCheckBox" Content="تذكر بيانات تسجيل الدخول" 
                                          FontSize="14" IsChecked="False" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Regional Settings -->
                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" 
                        CornerRadius="8" Padding="20">
                    <StackPanel>
                        <TextBlock Text="🌍 الإعدادات الإقليمية" FontSize="18" FontWeight="Bold" 
                                   Foreground="#495057" Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="15"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <StackPanel Grid.Row="0">
                                <TextBlock Text="إعدادات التنسيق الحالية (من نظام ويندوز)" 
                                           FontWeight="Bold" Margin="0,0,0,10"/>
                                <TextBlock x:Name="CurrentFormatTextBlock" 
                                           Background="#E3F2FD" Padding="15" 
                                           FontFamily="Consolas" FontSize="12"
                                           Text="جاري تحميل الإعدادات..."/>
                            </StackPanel>
                            
                            <StackPanel Grid.Row="2" Orientation="Horizontal">
                                <Button x:Name="OpenWindowsSettingsButton" 
                                        Content="🔧 فتح إعدادات ويندوز"
                                        Height="35" Padding="15,0" FontSize="14"
                                        Background="#4CAF50" Foreground="White"
                                        BorderThickness="0" Margin="0,0,10,0"
                                        Click="OpenWindowsSettingsButton_Click"/>
                                
                                <Button x:Name="RefreshFormatButton" 
                                        Content="🔄 تحديث"
                                        Height="35" Padding="15,0" FontSize="14"
                                        Background="#2196F3" Foreground="White"
                                        BorderThickness="0"
                                        Click="RefreshFormatButton_Click"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="0,1,0,0">
            <Grid Margin="20,0">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button x:Name="SaveButton" 
                            Content="💾 حفظ الإعدادات"
                            Height="40" Padding="20,0" FontSize="14"
                            Background="#28A745" Foreground="White"
                            BorderThickness="0" Margin="0,0,10,0"
                            Click="SaveButton_Click"/>
                    
                    <Button x:Name="ResetButton" 
                            Content="🔄 استعادة الافتراضي"
                            Height="40" Padding="20,0" FontSize="14"
                            Background="#6C757D" Foreground="White"
                            BorderThickness="0" Margin="0,0,10,0"
                            Click="ResetButton_Click"/>
                    
                    <Button x:Name="CancelButton" 
                            Content="❌ إلغاء"
                            Height="40" Padding="20,0" FontSize="14"
                            Background="#DC3545" Foreground="White"
                            BorderThickness="0"
                            Click="CancelButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
