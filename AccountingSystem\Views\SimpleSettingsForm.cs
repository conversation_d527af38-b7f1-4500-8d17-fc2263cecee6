using System;
using System.Drawing;
using System.Globalization;
using System.Windows.Forms;

namespace AccountingSystem.Views
{
    public partial class SimpleSettingsForm : Form
    {
        public SimpleSettingsForm()
        {
            InitializeComponent();
            LoadCurrentSettings();
        }

        private void InitializeComponent()
        {
            this.Text = "إعدادات النظام - نظام المحاسبة";
            this.Size = new Size(600, 500);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // Header Panel
            var headerPanel = new Panel
            {
                Dock = DockStyle.Top,
                Height = 60,
                BackColor = Color.FromArgb(33, 150, 243)
            };

            var headerLabel = new Label
            {
                Text = "⚙️ إعدادات النظام",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter
            };

            headerPanel.Controls.Add(headerLabel);
            this.Controls.Add(headerPanel);

            // Main Panel
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20),
                BackColor = Color.White
            };

            // Company Info Group
            var companyGroup = new GroupBox
            {
                Text = "🏢 معلومات الشركة",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Size = new Size(540, 120),
                Location = new Point(10, 10)
            };

            var companyNameLabel = new Label
            {
                Text = "اسم الشركة:",
                Location = new Point(400, 25),
                Size = new Size(80, 20)
            };

            var companyNameText = new TextBox
            {
                Text = "شركة المحاسبة المتقدمة",
                Location = new Point(150, 25),
                Size = new Size(240, 20)
            };

            var taxNumberLabel = new Label
            {
                Text = "الرقم الضريبي:",
                Location = new Point(400, 55),
                Size = new Size(80, 20)
            };

            var taxNumberText = new TextBox
            {
                Text = "300123456789003",
                Location = new Point(150, 55),
                Size = new Size(240, 20)
            };

            var phoneLabel = new Label
            {
                Text = "رقم الهاتف:",
                Location = new Point(400, 85),
                Size = new Size(80, 20)
            };

            var phoneText = new TextBox
            {
                Text = "+966 11 123 4567",
                Location = new Point(150, 85),
                Size = new Size(240, 20)
            };

            companyGroup.Controls.AddRange(new Control[] {
                companyNameLabel, companyNameText,
                taxNumberLabel, taxNumberText,
                phoneLabel, phoneText
            });

            // Format Info Group
            var formatGroup = new GroupBox
            {
                Text = "🌍 إعدادات التنسيق (من نظام ويندوز)",
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Size = new Size(540, 150),
                Location = new Point(10, 140)
            };

            var formatInfo = new TextBox
            {
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                Location = new Point(10, 25),
                Size = new Size(520, 115),
                BackColor = Color.FromArgb(240, 248, 255),
                Font = new Font("Consolas", 9)
            };

            formatGroup.Controls.Add(formatInfo);

            // Buttons Panel
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = Color.FromArgb(248, 249, 250)
            };

            var saveButton = new Button
            {
                Text = "💾 حفظ الإعدادات",
                Size = new Size(120, 35),
                Location = new Point(450, 12),
                BackColor = Color.FromArgb(40, 167, 69),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            var windowsSettingsButton = new Button
            {
                Text = "🔧 إعدادات ويندوز",
                Size = new Size(120, 35),
                Location = new Point(320, 12),
                BackColor = Color.FromArgb(23, 162, 184),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            var closeButton = new Button
            {
                Text = "❌ إغلاق",
                Size = new Size(100, 35),
                Location = new Point(210, 12),
                BackColor = Color.FromArgb(108, 117, 125),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat
            };

            saveButton.Click += SaveButton_Click;
            windowsSettingsButton.Click += WindowsSettingsButton_Click;
            closeButton.Click += (s, e) => this.Close();

            buttonPanel.Controls.AddRange(new Control[] {
                saveButton, windowsSettingsButton, closeButton
            });

            mainPanel.Controls.AddRange(new Control[] {
                companyGroup, formatGroup
            });

            this.Controls.AddRange(new Control[] {
                mainPanel, buttonPanel
            });

            // Store references for later use
            this.companyNameTextBox = companyNameText;
            this.taxNumberTextBox = taxNumberText;
            this.phoneTextBox = phoneText;
            this.formatInfoTextBox = formatInfo;
        }

        private TextBox companyNameTextBox;
        private TextBox taxNumberTextBox;
        private TextBox phoneTextBox;
        private TextBox formatInfoTextBox;

        private void LoadCurrentSettings()
        {
            try
            {
                var culture = CultureInfo.CurrentCulture;
                var now = DateTime.Now;
                var sampleAmount = 1234.56m;

                var formatText = string.Format(
                    "🌍 المنطقة: {0}\r\n" +
                    "📅 التاريخ: {1}\r\n" +
                    "🕐 الوقت: {2}\r\n" +
                    "💰 العملة: {3}\r\n" +
                    "🔢 الأرقام: {4}\r\n" +
                    "📊 النسبة: {5}\r\n\r\n" +
                    "رمز العملة: {6}\r\n" +
                    "فاصل الآلاف: '{7}'\r\n" +
                    "الفاصلة العشرية: '{8}'",
                    culture.DisplayName,
                    now.ToString("d", culture),
                    now.ToString("t", culture),
                    sampleAmount.ToString("C", culture),
                    sampleAmount.ToString("N2", culture),
                    (15.5m).ToString("N2", culture) + "%",
                    culture.NumberFormat.CurrencySymbol,
                    culture.NumberFormat.NumberGroupSeparator,
                    culture.NumberFormat.NumberDecimalSeparator
                );

                formatInfoTextBox.Text = formatText;
            }
            catch (Exception ex)
            {
                formatInfoTextBox.Text = "خطأ في تحميل معلومات التنسيق: " + ex.Message;
            }
        }

        private void SaveButton_Click(object sender, EventArgs e)
        {
            try
            {
                var message = "تم حفظ الإعدادات بنجاح!\r\n\r\n" +
                             "معلومات الشركة:\r\n" +
                             "• الاسم: " + companyNameTextBox.Text + "\r\n" +
                             "• الرقم الضريبي: " + taxNumberTextBox.Text + "\r\n" +
                             "• الهاتف: " + phoneTextBox.Text + "\r\n\r\n" +
                             "ملاحظة: إعدادات التنسيق تتبع نظام ويندوز تلقائياً.";

                MessageBox.Show(message, "تم الحفظ", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حفظ الإعدادات: " + ex.Message, "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void WindowsSettingsButton_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start("ms-settings:regionlanguage");
            }
            catch
            {
                try
                {
                    System.Diagnostics.Process.Start("intl.cpl");
                }
                catch
                {
                    MessageBox.Show(
                        "لا يمكن فتح إعدادات ويندوز تلقائياً.\r\n\r\n" +
                        "يرجى فتح إعدادات ويندوز يدوياً:\r\n" +
                        "1. اضغط على زر ابدأ\r\n" +
                        "2. اختر الإعدادات\r\n" +
                        "3. اذهب إلى الوقت واللغة\r\n" +
                        "4. اختر المنطقة",
                        "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }
    }
}
