<Window x:Class="AccountingSystem.Views.AddJournalEntryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة قيد يومي جديد - نظام المحاسبة المتقدم" 
        Height="650" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#673AB7">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="📝" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إضافة قيد يومي جديد" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="30,20">
                
                <!-- Entry Header Info -->
                <Border Background="#F5F5F5" CornerRadius="5" Padding="15" Margin="0,0,0,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0">
                            <TextBlock Text="رقم القيد *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="EntryNumberTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="White" IsReadOnly="True"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" Margin="10,0">
                            <TextBlock Text="التاريخ *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="EntryDateTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="White"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="المرجع" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox x:Name="ReferenceTextBox" 
                                     Height="35" FontSize="14" Padding="10"
                                     Background="White"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- Description -->
                <TextBlock Text="وصف القيد *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox" 
                         Height="60" FontSize="14" Padding="10"
                         Background="#F9F9F9" Margin="0,0,0,20"
                         TextWrapping="Wrap" AcceptsReturn="True"/>
                
                <!-- Journal Entry Lines -->
                <TextBlock Text="تفاصيل القيد" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                
                <Border BorderBrush="#DDD" BorderThickness="1" CornerRadius="5" Margin="0,0,0,10">
                    <DataGrid x:Name="JournalLinesDataGrid" 
                              AutoGenerateColumns="False"
                              CanUserAddRows="True"
                              CanUserDeleteRows="True"
                              GridLinesVisibility="All"
                              HeadersVisibility="Column"
                              Background="White"
                              FontSize="13"
                              MinHeight="200">
                        
                        <DataGrid.Columns>
                            <DataGridComboBoxColumn Header="الحساب *" Width="200">
                                <DataGridComboBoxColumn.ElementStyle>
                                    <Style TargetType="ComboBox">
                                        <Setter Property="ItemsSource" Value="{Binding RelativeSource={RelativeSource AncestorType=Window}, Path=Accounts}"/>
                                        <Setter Property="DisplayMemberPath" Value="DisplayName"/>
                                        <Setter Property="SelectedValuePath" Value="AccountNumber"/>
                                    </Style>
                                </DataGridComboBoxColumn.ElementStyle>
                            </DataGridComboBoxColumn>
                            <DataGridTextColumn Header="البيان" Binding="{Binding Description}" Width="*"/>
                            <DataGridTextColumn Header="مدين" Binding="{Binding DebitAmount}" Width="120"/>
                            <DataGridTextColumn Header="دائن" Binding="{Binding CreditAmount}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Border>
                
                <!-- Add/Remove Line Buttons -->
                <StackPanel Orientation="Horizontal" Margin="0,10,0,20">
                    <Button Content="➕ إضافة سطر" 
                            Width="120" Height="35" FontSize="13"
                            Background="#4CAF50" Foreground="White" Margin="0,0,10,0"
                            Click="AddLineButton_Click"/>
                    <Button Content="➖ حذف سطر" 
                            Width="120" Height="35" FontSize="13"
                            Background="#F44336" Foreground="White"
                            Click="RemoveLineButton_Click"/>
                </StackPanel>
                
                <!-- Totals -->
                <Border Background="#E3F2FD" CornerRadius="5" Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="150"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0" Text="الإجماليات:" FontSize="14" FontWeight="Bold" VerticalAlignment="Center"/>
                        
                        <StackPanel Grid.Column="1">
                            <TextBlock Text="إجمالي المدين:" FontSize="12" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TotalDebitLabel" Text="0.00" FontSize="14" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="#2196F3"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="2">
                            <TextBlock Text="إجمالي الدائن:" FontSize="12" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TotalCreditLabel" Text="0.00" FontSize="14" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="#2196F3"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="3">
                            <TextBlock Text="الفرق:" FontSize="12" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="DifferenceLabel" Text="0.00" FontSize="14" FontWeight="Bold"
                                       HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ كمسودة" 
                        Width="140" Height="40" FontSize="14"
                        Background="#FF9800" Foreground="White" Margin="10,0"
                        Click="SaveDraftButton_Click"/>
                <Button Content="✅ حفظ وترحيل" 
                        Width="140" Height="40" FontSize="14" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White" Margin="10,0"
                        Click="SaveAndPostButton_Click"/>
                <Button Content="🔄 مسح الحقول" 
                        Width="140" Height="40" FontSize="14"
                        Background="#607D8B" Foreground="White" Margin="10,0"
                        Click="ClearButton_Click"/>
                <Button Content="❌ إلغاء" 
                        Width="140" Height="40" FontSize="14"
                        Background="#F44336" Foreground="White" Margin="10,0"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
