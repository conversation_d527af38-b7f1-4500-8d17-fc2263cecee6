<Window x:Class="AccountingSystem.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="نظام المحاسبة المتقدم" 
        Height="800" Width="1200"
        MinHeight="600" MinWidth="900"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI">
    
    <materialDesign:Card materialDesign:ElevationAssist.Elevation="Dp4"
                         UniformCornerRadius="10"
                         Margin="10">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="30"/>
            </Grid.RowDefinitions>
            
            <!-- Title Bar -->
            <Grid Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- Title -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <materialDesign:PackIcon Kind="Calculator" 
                                           Width="32" Height="32" 
                                           Foreground="{DynamicResource MaterialDesignBody}"
                                           VerticalAlignment="Center"/>
                    <TextBlock Text="نظام المحاسبة المتقدم" 
                             FontSize="20" FontWeight="Bold"
                             VerticalAlignment="Center" 
                             Margin="10,0,0,0"
                             Foreground="{DynamicResource MaterialDesignBody}"/>
                </StackPanel>
                
                <!-- Window Controls -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="0,0,10,0">
                    <Button x:Name="MinimizeButton" 
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="30" Height="30" 
                            Click="MinimizeButton_Click">
                        <materialDesign:PackIcon Kind="WindowMinimize" Width="16" Height="16"/>
                    </Button>
                    <Button x:Name="MaximizeButton" 
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="30" Height="30" 
                            Click="MaximizeButton_Click">
                        <materialDesign:PackIcon Kind="WindowMaximize" Width="16" Height="16"/>
                    </Button>
                    <Button x:Name="CloseButton" 
                            Style="{StaticResource MaterialDesignIconButton}"
                            Width="30" Height="30" 
                            Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                    </Button>
                </StackPanel>
            </Grid>
            
            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <!-- Side Menu -->
                <materialDesign:Card Grid.Column="0" 
                                   Background="{DynamicResource MaterialDesignCardBackground}"
                                   Margin="5">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="10">
                            <!-- Dashboard -->
                            <Button x:Name="DashboardButton" 
                                  Style="{StaticResource MenuButtonStyle}"
                                  Click="DashboardButton_Click">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ViewDashboard" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="لوحة التحكم"/>
                                </StackPanel>
                            </Button>
                            
                            <!-- Accounts Management -->
                            <Expander Header="إدارة الحسابات" 
                                    Style="{StaticResource MaterialDesignExpander}"
                                    Margin="0,5">
                                <StackPanel Margin="20,5,0,5">
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40" Click="AccountsButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="AccountTree" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="دليل الحسابات"/>
                                        </StackPanel>
                                    </Button>
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40" Click="AddAccountButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="AccountPlus" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="إضافة حساب"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Expander>
                            
                            <!-- Journal Entries -->
                            <Expander Header="القيود اليومية" 
                                    Style="{StaticResource MaterialDesignExpander}"
                                    Margin="0,5">
                                <StackPanel Margin="20,5,0,5">
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40" Click="AddJournalEntryButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="BookOpen" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="قيد جديد"/>
                                        </StackPanel>
                                    </Button>
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40" Click="JournalEntriesButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="BookMultiple" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="عرض القيود"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Expander>
                            
                            <!-- Invoices -->
                            <Expander Header="الفواتير" 
                                    Style="{StaticResource MaterialDesignExpander}"
                                    Margin="0,5">
                                <StackPanel Margin="20,5,0,5">
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40" Click="AddSalesInvoiceButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Receipt" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="فاتورة مبيعات"/>
                                        </StackPanel>
                                    </Button>
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40" Click="AddPurchaseInvoiceButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ShoppingCart" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="فاتورة مشتريات"/>
                                        </StackPanel>
                                    </Button>
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40" Click="InvoicesButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="عرض الفواتير"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Expander>
                            
                            <!-- Reports -->
                            <Expander Header="التقارير" 
                                    Style="{StaticResource MaterialDesignExpander}"
                                    Margin="0,5">
                                <StackPanel Margin="20,5,0,5">
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40" Click="ReportsButton_Click">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ChartMultiple" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="جميع التقارير"/>
                                        </StackPanel>
                                    </Button>
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="ChartLine" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="قائمة الدخل"/>
                                        </StackPanel>
                                    </Button>
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="Scale" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="الميزانية العمومية"/>
                                        </StackPanel>
                                    </Button>
                                    <Button Style="{StaticResource MenuButtonStyle}" Height="40">
                                        <StackPanel Orientation="Horizontal">
                                            <materialDesign:PackIcon Kind="CashFlow" Width="16" Height="16" Margin="0,0,8,0"/>
                                            <TextBlock Text="التدفقات النقدية"/>
                                        </StackPanel>
                                    </Button>
                                </StackPanel>
                            </Expander>
                            
                            <!-- Settings -->
                            <Button Style="{StaticResource MenuButtonStyle}" Margin="0,10,0,0">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Settings" Width="20" Height="20" Margin="0,0,10,0"/>
                                    <TextBlock Text="الإعدادات"/>
                                </StackPanel>
                            </Button>
                        </StackPanel>
                    </ScrollViewer>
                </materialDesign:Card>
                
                <!-- Main Content Area -->
                <materialDesign:Card Grid.Column="1" 
                                   Background="{DynamicResource MaterialDesignCardBackground}"
                                   Margin="5">
                    <Grid x:Name="MainContentGrid">
                        <!-- Dashboard Content -->
                        <Grid x:Name="DashboardContent" Visibility="Visible">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Margin="20">
                                    <TextBlock Text="لوحة التحكم" Style="{StaticResource TitleStyle}"/>
                                    
                                    <!-- Statistics Cards -->
                                    <Grid Margin="0,20">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="*"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <!-- Total Revenue Card -->
                                        <materialDesign:Card Grid.Column="0" Style="{StaticResource CardStyle}" 
                                                           Background="{DynamicResource MaterialDesignSelection}">
                                            <StackPanel>
                                                <materialDesign:PackIcon Kind="TrendingUp" Width="40" Height="40" 
                                                                       HorizontalAlignment="Center" 
                                                                       Foreground="White"/>
                                                <TextBlock Text="إجمالي الإيرادات" 
                                                         HorizontalAlignment="Center" 
                                                         FontWeight="Bold" 
                                                         Foreground="White" 
                                                         Margin="0,10,0,5"/>
                                                <TextBlock Text="0.00 ر.س" 
                                                         HorizontalAlignment="Center" 
                                                         FontSize="18" 
                                                         FontWeight="Bold" 
                                                         Foreground="White"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                        
                                        <!-- Total Expenses Card -->
                                        <materialDesign:Card Grid.Column="1" Style="{StaticResource CardStyle}" 
                                                           Background="{DynamicResource MaterialDesignValidationErrorBrush}">
                                            <StackPanel>
                                                <materialDesign:PackIcon Kind="TrendingDown" Width="40" Height="40" 
                                                                       HorizontalAlignment="Center" 
                                                                       Foreground="White"/>
                                                <TextBlock Text="إجمالي المصروفات" 
                                                         HorizontalAlignment="Center" 
                                                         FontWeight="Bold" 
                                                         Foreground="White" 
                                                         Margin="0,10,0,5"/>
                                                <TextBlock Text="0.00 ر.س" 
                                                         HorizontalAlignment="Center" 
                                                         FontSize="18" 
                                                         FontWeight="Bold" 
                                                         Foreground="White"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                        
                                        <!-- Net Profit Card -->
                                        <materialDesign:Card Grid.Column="2" Style="{StaticResource CardStyle}" 
                                                           Background="Green">
                                            <StackPanel>
                                                <materialDesign:PackIcon Kind="CurrencyUsd" Width="40" Height="40" 
                                                                       HorizontalAlignment="Center" 
                                                                       Foreground="White"/>
                                                <TextBlock Text="صافي الربح" 
                                                         HorizontalAlignment="Center" 
                                                         FontWeight="Bold" 
                                                         Foreground="White" 
                                                         Margin="0,10,0,5"/>
                                                <TextBlock Text="0.00 ر.س" 
                                                         HorizontalAlignment="Center" 
                                                         FontSize="18" 
                                                         FontWeight="Bold" 
                                                         Foreground="White"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                        
                                        <!-- Total Accounts Card -->
                                        <materialDesign:Card Grid.Column="3" Style="{StaticResource CardStyle}" 
                                                           Background="Orange">
                                            <StackPanel>
                                                <materialDesign:PackIcon Kind="AccountMultiple" Width="40" Height="40" 
                                                                       HorizontalAlignment="Center" 
                                                                       Foreground="White"/>
                                                <TextBlock Text="عدد الحسابات" 
                                                         HorizontalAlignment="Center" 
                                                         FontWeight="Bold" 
                                                         Foreground="White" 
                                                         Margin="0,10,0,5"/>
                                                <TextBlock Text="0" 
                                                         HorizontalAlignment="Center" 
                                                         FontSize="18" 
                                                         FontWeight="Bold" 
                                                         Foreground="White"/>
                                            </StackPanel>
                                        </materialDesign:Card>
                                    </Grid>
                                    
                                    <!-- Recent Activities -->
                                    <materialDesign:Card Style="{StaticResource CardStyle}" Margin="0,20,0,0">
                                        <StackPanel>
                                            <TextBlock Text="الأنشطة الأخيرة" Style="{StaticResource SubtitleStyle}"/>
                                            <Separator Margin="0,10"/>
                                            <TextBlock Text="لا توجد أنشطة حديثة" 
                                                     HorizontalAlignment="Center" 
                                                     Margin="0,20" 
                                                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                                        </StackPanel>
                                    </materialDesign:Card>
                                </StackPanel>
                            </ScrollViewer>
                        </Grid>
                    </Grid>
                </materialDesign:Card>
            </Grid>
            
            <!-- Status Bar -->
            <Grid Grid.Row="2" Background="{DynamicResource MaterialDesignDivider}">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" 
                         Text="جاهز" 
                         VerticalAlignment="Center" 
                         Margin="10,0" 
                         FontSize="12"/>
                
                <TextBlock Grid.Column="1" 
                         Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='yyyy/MM/dd HH:mm'}" 
                         VerticalAlignment="Center" 
                         Margin="10,0" 
                         FontSize="12"
                         xmlns:sys="clr-namespace:System;assembly=mscorlib"/>
            </Grid>
        </Grid>
    </materialDesign:Card>
</Window>

