using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class AccountsWindow : Window
    {
        private List<AccountItem> accounts;

        public AccountsWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            accounts = new List<AccountItem>
            {
                new AccountItem { AccountNumber = "1001", AccountName = "النقدية", AccountType = "الأصول", Balance = "50,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "1002", AccountName = "البنك الأهلي", AccountType = "الأصول", Balance = "125,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "1003", AccountName = "العملاء", AccountType = "الأصول", Balance = "75,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "2001", AccountName = "الموردون", AccountType = "الخصوم", Balance = "30,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "2002", AccountName = "قروض قصيرة الأجل", AccountType = "الخصوم", Balance = "100,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "3001", AccountName = "رأس المال", AccountType = "حقوق الملكية", Balance = "200,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "4001", AccountName = "مبيعات", AccountType = "الإيرادات", Balance = "150,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "4002", AccountName = "إيرادات أخرى", AccountType = "الإيرادات", Balance = "25,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "5001", AccountName = "تكلفة البضاعة المباعة", AccountType = "المصروفات", Balance = "80,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "5002", AccountName = "مصروفات إدارية", AccountType = "المصروفات", Balance = "35,000.00", Currency = "ر.س" },
                new AccountItem { AccountNumber = "5003", AccountName = "مصروفات تسويقية", AccountType = "المصروفات", Balance = "20,000.00", Currency = "ر.س" }
            };

            AccountsDataGrid.ItemsSource = accounts;
            UpdateStatusLabel();
        }

        private void UpdateStatusLabel()
        {
            StatusLabel.Text = $"جاهز - {accounts.Count} حساب";
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox.Text == "البحث في الحسابات...")
            {
                textBox.Text = "";
                textBox.Foreground = Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = "البحث في الحسابات...";
                textBox.Foreground = Brushes.Gray;
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            // Implement search functionality
            MessageBox.Show("سيتم تطبيق البحث قريباً", "البحث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var addAccountWindow = new AddAccountWindow();
            if (addAccountWindow.ShowDialog() == true)
            {
                // Refresh the accounts list
                LoadSampleData();
            }
        }

        private void EditAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var account = button?.DataContext as AccountItem;
            if (account != null)
            {
                MessageBox.Show($"تعديل الحساب: {account.AccountName}", "تعديل حساب", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var account = button?.DataContext as AccountItem;
            if (account != null)
            {
                var result = MessageBox.Show($"هل تريد حذف الحساب: {account.AccountName}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                if (result == MessageBoxResult.Yes)
                {
                    accounts.Remove(account);
                    AccountsDataGrid.Items.Refresh();
                    UpdateStatusLabel();
                }
            }
        }

        private void ViewAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var account = button?.DataContext as AccountItem;
            if (account != null)
            {
                MessageBox.Show($"عرض تفاصيل الحساب: {account.AccountName}\nالرقم: {account.AccountNumber}\nالنوع: {account.AccountType}\nالرصيد: {account.Balance}", 
                               "تفاصيل الحساب", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير البيانات قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم استيراد البيانات قريباً", "استيراد", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class AccountItem
    {
        public string AccountNumber { get; set; }
        public string AccountName { get; set; }
        public string AccountType { get; set; }
        public string Balance { get; set; }
        public string Currency { get; set; }
    }
}
