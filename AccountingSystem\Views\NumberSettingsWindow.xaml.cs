using System;
using System.Windows;

namespace AccountingSystem.Views
{
    public partial class NumberSettingsWindow : Window
    {
        public NumberSettingsWindow()
        {
            try
            {
                InitializeComponent();
                LoadCurrentSettings();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل نافذة إعدادات الأرقام: " + ex.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCurrentSettings()
        {
            try
            {
                // Load current number format setting
                // For now, default to Hindi numbers (English digits)
                HindiNumbersRadio.IsChecked = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل الإعدادات الحالية: " + ex.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string selectedType = "";
                string example = "";
                
                if (ArabicNumbersRadio.IsChecked == true)
                {
                    selectedType = "الأرقام العربية";
                    example = "١٬٢٣٤٫٥٦";
                }
                else if (HindiNumbersRadio.IsChecked == true)
                {
                    selectedType = "الأرقام الهندية";
                    example = "1,234.56";
                }

                var message = "تم حفظ إعدادات الأرقام بنجاح!\n\n" +
                             "النوع المختار: " + selectedType + "\n" +
                             "مثال: " + example + " ريال\n\n" +
                             "ملاحظة: قد تحتاج لإعادة تشغيل البرنامج لتطبيق التغييرات.";

                MessageBox.Show(message, "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                
                // Here you would normally save the setting to a config file or database
                // For now, we'll just close the window
                this.DialogResult = true;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حفظ الإعدادات: " + ex.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.DialogResult = false;
            this.Close();
        }
    }
}
