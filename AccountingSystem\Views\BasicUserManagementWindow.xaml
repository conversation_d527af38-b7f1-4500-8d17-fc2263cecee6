<Window x:Class="AccountingSystem.Views.BasicUserManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة المستخدمين - نظام المحاسبة" 
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="80"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#DC3545">
            <TextBlock Text="👥 إدارة المستخدمين" 
                       FontSize="18" FontWeight="Bold" 
                       Foreground="White" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"/>
        </Border>
        
        <!-- Content -->
        <Grid Grid.Row="1" Margin="10">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Statistics -->
            <Border Grid.Row="0" Background="#E9ECEF" Padding="10" Margin="0,0,0,10">
                <TextBlock x:Name="StatsTextBlock" 
                           Text="📊 الإحصائيات: إجمالي المستخدمين: 3 | النشطون: 2 | المعطلون: 1 | المديرون: 1"
                           FontWeight="Bold" 
                           Foreground="#495057"
                           HorizontalAlignment="Center"/>
            </Border>
            
            <!-- Users List -->
            <GroupBox Grid.Row="1" Header="📋 قائمة المستخدمين" FontWeight="Bold">
                <ListView x:Name="UsersListView"
                          SelectionMode="Single">
                    <ListView.View>
                        <GridView>
                            <GridViewColumn Header="اسم المستخدم" Width="120" DisplayMemberBinding="{Binding Username}"/>
                            <GridViewColumn Header="الاسم الكامل" Width="150" DisplayMemberBinding="{Binding FullName}"/>
                            <GridViewColumn Header="الدور" Width="100" DisplayMemberBinding="{Binding Role}"/>
                            <GridViewColumn Header="الحالة" Width="80" DisplayMemberBinding="{Binding Status}"/>
                            <GridViewColumn Header="آخر دخول" Width="120" DisplayMemberBinding="{Binding LastLogin}"/>
                            <GridViewColumn Header="البريد الإلكتروني" Width="200" DisplayMemberBinding="{Binding Email}"/>
                        </GridView>
                    </ListView.View>
                </ListView>
            </GroupBox>
        </Grid>
        
        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F8F9FA">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="10">
                <Button x:Name="AddButton" Content="➕ إضافة مستخدم" 
                        Width="120" Height="35" Margin="5"
                        Background="#28A745" Foreground="White" 
                        Click="AddButton_Click"/>
                <Button x:Name="EditButton" Content="✏️ تعديل" 
                        Width="100" Height="35" Margin="5"
                        Background="#FFC107" Foreground="Black" 
                        Click="EditButton_Click"/>
                <Button x:Name="DeleteButton" Content="🗑️ حذف" 
                        Width="100" Height="35" Margin="5"
                        Background="#DC3545" Foreground="White" 
                        Click="DeleteButton_Click"/>
                <Button x:Name="ResetPasswordButton" Content="🔑 إعادة تعيين كلمة المرور" 
                        Width="180" Height="35" Margin="5"
                        Background="#17A2B8" Foreground="White" 
                        Click="ResetPasswordButton_Click"/>
                <Button x:Name="RefreshButton" Content="🔄 تحديث" 
                        Width="100" Height="35" Margin="5"
                        Background="#6C757D" Foreground="White" 
                        Click="RefreshButton_Click"/>
                <Button x:Name="CloseButton" Content="❌ إغلاق" 
                        Width="100" Height="35" Margin="5"
                        Background="#6C757D" Foreground="White" 
                        Click="CloseButton_Click"/>
            </StackPanel>
        </Border>
        
    </Grid>
</Window>
