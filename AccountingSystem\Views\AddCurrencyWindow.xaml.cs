using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingSystem.Views
{
    public partial class AddCurrencyWindow : Window
    {
        public AddCurrencyWindow()
        {
            InitializeComponent();
            UpdatePreview();
        }

        private void UpdatePreview()
        {
            var symbol = CurrencySymbolTextBox.Text;
            var position = ((ComboBoxItem)SymbolPositionComboBox.SelectedItem)?.Content.ToString() ?? "";
            var decimals = int.Parse(((ComboBoxItem)DecimalPlacesComboBox.SelectedItem)?.Content.ToString() ?? "2");
            var thousandsSep = GetSeparator(ThousandsSeparatorComboBox);
            var decimalSep = GetSeparator(DecimalSeparatorComboBox);
            
            var amount = 1234.56m;
            var formattedAmount = FormatAmount(amount, decimals, thousandsSep, decimalSep);
            
            if (position.Contains("قبل"))
            {
                CurrencyPreviewTextBlock.Text = $"المبلغ: {symbol} {formattedAmount}";
            }
            else
            {
                CurrencyPreviewTextBlock.Text = $"المبلغ: {formattedAmount} {symbol}";
            }
        }

        private string GetSeparator(ComboBox comboBox)
        {
            var selected = ((ComboBoxItem)comboBox.SelectedItem)?.Content.ToString() ?? "";
            
            if (selected.Contains("فاصلة")) return ",";
            if (selected.Contains("نقطة")) return ".";
            if (selected.Contains("مسافة")) return " ";
            if (selected.Contains("بدون")) return "";
            
            return ",";
        }

        private string FormatAmount(decimal amount, int decimals, string thousandsSep, string decimalSep)
        {
            var integerPart = ((long)amount).ToString();
            var decimalPart = "";
            
            if (decimals > 0)
            {
                var decimalValue = amount - (long)amount;
                decimalPart = decimalValue.ToString($"F{decimals}").Substring(1); // Remove "0"
                decimalPart = decimalPart.Replace(".", decimalSep);
            }
            
            // Add thousands separator
            if (!string.IsNullOrEmpty(thousandsSep) && integerPart.Length > 3)
            {
                var formatted = "";
                var counter = 0;
                for (int i = integerPart.Length - 1; i >= 0; i--)
                {
                    if (counter > 0 && counter % 3 == 0)
                    {
                        formatted = thousandsSep + formatted;
                    }
                    formatted = integerPart[i] + formatted;
                    counter++;
                }
                integerPart = formatted;
            }
            
            return integerPart + decimalPart;
        }

        private void PreviewButton_Click(object sender, RoutedEventArgs e)
        {
            UpdatePreview();
        }

        private void SaveCurrencyButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                var message = "تم حفظ العملة بنجاح!\n\n" +
                             "اسم العملة: " + CurrencyNameTextBox.Text + "\n" +
                             "رمز العملة: " + CurrencySymbolTextBox.Text + "\n" +
                             "الكود الدولي: " + CurrencyCodeTextBox.Text + "\n" +
                             "سعر الصرف: " + ExchangeRateTextBox.Text + "\n" +
                             "عدد الخانات العشرية: " + (((ComboBoxItem)DecimalPlacesComboBox.SelectedItem)?.Content ?? "2");

                MessageBox.Show(message, "تم حفظ العملة", MessageBoxButton.OK, MessageBoxImage.Information);
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد مسح جميع الحقول؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                ClearAllFields();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء العملية؟ سيتم فقدان البيانات المدخلة.", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result == MessageBoxResult.Yes)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(CurrencyNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العملة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CurrencyNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CurrencyNameEnTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العملة بالإنجليزية", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CurrencyNameEnTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CurrencySymbolTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال رمز العملة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CurrencySymbolTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CurrencyCodeTextBox.Text) || CurrencyCodeTextBox.Text.Length != 3)
            {
                MessageBox.Show("يرجى إدخال كود دولي صحيح (3 أحرف)", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CurrencyCodeTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(ExchangeRateTextBox.Text, out decimal exchangeRate) || exchangeRate <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر صرف صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ExchangeRateTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearAllFields()
        {
            CurrencyNameTextBox.Clear();
            CurrencyNameEnTextBox.Clear();
            CurrencySymbolTextBox.Clear();
            CurrencyCodeTextBox.Clear();
            ExchangeRateTextBox.Text = "1.00";
            DecimalPlacesComboBox.SelectedIndex = 2;
            SymbolPositionComboBox.SelectedIndex = 0;
            ThousandsSeparatorComboBox.SelectedIndex = 0;
            DecimalSeparatorComboBox.SelectedIndex = 0;
            IsActiveCheckBox.IsChecked = true;
            
            UpdatePreview();
            CurrencyNameTextBox.Focus();
        }
    }
}
