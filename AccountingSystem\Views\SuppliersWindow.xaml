<Window x:Class="AccountingSystem.Views.SuppliersWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الموردين - نظام المحاسبة المتقدم" 
        Height="650" Width="1100"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#607D8B">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="🏭" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة الموردين والعملاء" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="➕ مورد جديد" 
                            Background="#4CAF50" Foreground="White"
                            Width="120" Height="35" Margin="5,0"
                            Click="AddSupplierButton_Click"/>
                    <Button Content="👥 عميل جديد" 
                            Background="#2196F3" Foreground="White"
                            Width="120" Height="35" Margin="5,0"
                            Click="AddCustomerButton_Click"/>
                    <Button Content="إغلاق" 
                            Background="#F44336" Foreground="White"
                            Width="80" Height="35" Margin="5,0"
                            Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Search and Filter -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="النوع:" FontSize="12" Margin="0,0,0,5"/>
                    <ComboBox x:Name="TypeComboBox" Height="35" FontSize="14">
                        <ComboBoxItem Content="الكل" IsSelected="True"/>
                        <ComboBoxItem Content="موردين"/>
                        <ComboBoxItem Content="عملاء"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="الحالة:" FontSize="12" Margin="0,0,0,5"/>
                    <ComboBox x:Name="StatusComboBox" Height="35" FontSize="14">
                        <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                        <ComboBoxItem Content="نشط"/>
                        <ComboBoxItem Content="غير نشط"/>
                        <ComboBoxItem Content="محظور"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="البحث:" FontSize="12" Margin="0,0,0,5"/>
                    <TextBox x:Name="SearchTextBox" Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" Text="البحث في الموردين والعملاء..." 
                             Foreground="#999"
                             GotFocus="SearchTextBox_GotFocus"
                             LostFocus="SearchTextBox_LostFocus"/>
                </StackPanel>
                
                <Button Grid.Column="3" Content="🔍 بحث"
                        Height="35" FontSize="14" Margin="10,25,0,0"
                        Background="#607D8B" Foreground="White"
                        Click="SearchButton_Click"/>
            </Grid>
            
            <!-- Suppliers/Customers List -->
            <Border Grid.Row="1" BorderBrush="#DDD" BorderThickness="1" CornerRadius="5">
                <DataGrid x:Name="SuppliersDataGrid" 
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          FontSize="13">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الكود" Binding="{Binding Code}" Width="80"/>
                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="*"/>
                        <DataGridTextColumn Header="النوع" Binding="{Binding Type}" Width="80"/>
                        <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                        <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="150"/>
                        <DataGridTextColumn Header="المدينة" Binding="{Binding City}" Width="100"/>
                        <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance}" Width="100"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                        <DataGridTemplateColumn Header="الإجراءات" Width="180">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="👁️" Width="30" Height="25" Margin="2"
                                                Background="#2196F3" Foreground="White"
                                                Click="ViewSupplierButton_Click"
                                                ToolTip="عرض"/>
                                        <Button Content="✏️" Width="30" Height="25" Margin="2"
                                                Background="#FF9800" Foreground="White"
                                                Click="EditSupplierButton_Click"
                                                ToolTip="تعديل"/>
                                        <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                                Background="#F44336" Foreground="White"
                                                Click="DeleteSupplierButton_Click"
                                                ToolTip="حذف"/>
                                        <Button Content="📊" Width="30" Height="25" Margin="2"
                                                Background="#4CAF50" Foreground="White"
                                                Click="SupplierReportButton_Click"
                                                ToolTip="تقرير"/>
                                        <Button Content="💰" Width="30" Height="25" Margin="2"
                                                Background="#9C27B0" Foreground="White"
                                                Click="PaymentButton_Click"
                                                ToolTip="دفعة"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>
        </Grid>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <TextBlock x:Name="StatusLabel" Text="جاهز - 0 مورد/عميل" FontSize="12" Foreground="#666" Margin="0,0,20,0"/>
                    <TextBlock Text="إجمالي أرصدة الموردين:" FontSize="12" Margin="0,0,5,0"/>
                    <TextBlock x:Name="TotalSuppliersBalanceLabel" Text="0.00 ر.س" FontWeight="Bold" Foreground="#F44336" Margin="0,0,20,0"/>
                    <TextBlock Text="إجمالي أرصدة العملاء:" FontSize="12" Margin="0,0,5,0"/>
                    <TextBlock x:Name="TotalCustomersBalanceLabel" Text="0.00 ر.س" FontWeight="Bold" Foreground="#4CAF50"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="📤 تصدير" Width="80" Height="30" Margin="5,0"
                            Background="#607D8B" Foreground="White"
                            Click="ExportButton_Click"/>
                    <Button Content="📥 استيراد" Width="80" Height="30" Margin="5,0"
                            Background="#795548" Foreground="White"
                            Click="ImportButton_Click"/>
                    <Button Content="🔄 تحديث" Width="80" Height="30" Margin="5,0"
                            Background="#9C27B0" Foreground="White"
                            Click="RefreshButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
