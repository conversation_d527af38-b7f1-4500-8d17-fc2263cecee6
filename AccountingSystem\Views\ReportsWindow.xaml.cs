using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class ReportsWindow : Window
    {
        public ReportsWindow()
        {
            InitializeComponent();
            UpdateLastUpdateTime();
        }

        private void UpdateLastUpdateTime()
        {
            LastUpdateLabel.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
        }

        private void ShowReportContent(string reportTitle, string reportContent)
        {
            WelcomeContent.Visibility = Visibility.Collapsed;
            ReportScrollViewer.Visibility = Visibility.Visible;
            
            ReportContent.Children.Clear();
            
            // Add report title
            var titleBlock = new TextBlock
            {
                Text = reportTitle,
                FontSize = 24,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 20),
                Foreground = new SolidColorBrush(Color.FromRgb(121, 85, 72))
            };
            ReportContent.Children.Add(titleBlock);
            
            // Add report content
            var contentBlock = new TextBlock
            {
                Text = reportContent,
                FontSize = 14,
                TextWrapping = TextWrapping.Wrap,
                LineHeight = 20
            };
            ReportContent.Children.Add(contentBlock);
            
            StatusLabel.Text = $"عرض تقرير: {reportTitle}";
            UpdateLastUpdateTime();
        }

        private void IncomeStatementButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"قائمة الدخل للفترة من 01/01/2024 إلى 31/12/2024

الإيرادات:
- مبيعات                           175,000.00 ر.س
- إيرادات أخرى                      25,000.00 ر.س
إجمالي الإيرادات                    200,000.00 ر.س

المصروفات:
- تكلفة البضاعة المباعة             80,000.00 ر.س
- مصروفات إدارية                   35,000.00 ر.س
- مصروفات تسويقية                  20,000.00 ر.س
- مصروفات أخرى                     15,000.00 ر.س
إجمالي المصروفات                   150,000.00 ر.س

صافي الربح                         50,000.00 ر.س";

            ShowReportContent("قائمة الدخل", reportContent);
        }

        private void BalanceSheetButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"الميزانية العمومية كما في 31/12/2024

الأصول:
الأصول المتداولة:
- النقدية                          50,000.00 ر.س
- البنك                           125,000.00 ر.س
- العملاء                         75,000.00 ر.س
- المخزون                         60,000.00 ر.س
إجمالي الأصول المتداولة            310,000.00 ر.س

الأصول الثابتة:
- المعدات                         100,000.00 ر.س
- الأثاث                          40,000.00 ر.س
إجمالي الأصول الثابتة              140,000.00 ر.س

إجمالي الأصول                     450,000.00 ر.س

الخصوم وحقوق الملكية:
الخصوم المتداولة:
- الموردون                        30,000.00 ر.س
- مصروفات مستحقة                  20,000.00 ر.س
إجمالي الخصوم المتداولة            50,000.00 ر.س

حقوق الملكية:
- رأس المال                      200,000.00 ر.س
- الأرباح المحتجزة               200,000.00 ر.س
إجمالي حقوق الملكية               400,000.00 ر.س

إجمالي الخصوم وحقوق الملكية        450,000.00 ر.س";

            ShowReportContent("الميزانية العمومية", reportContent);
        }

        private void CashFlowButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"قائمة التدفقات النقدية للفترة من 01/01/2024 إلى 31/12/2024

التدفقات النقدية من الأنشطة التشغيلية:
- المتحصلات من العملاء            180,000.00 ر.س
- المدفوعات للموردين             (120,000.00) ر.س
- المدفوعات للموظفين              (45,000.00) ر.س
- مدفوعات أخرى                   (10,000.00) ر.س
صافي التدفق من الأنشطة التشغيلية    5,000.00 ر.س

التدفقات النقدية من الأنشطة الاستثمارية:
- شراء معدات                     (50,000.00) ر.س
- بيع أصول                       10,000.00 ر.س
صافي التدفق من الأنشطة الاستثمارية  (40,000.00) ر.س

التدفقات النقدية من الأنشطة التمويلية:
- زيادة رأس المال                50,000.00 ر.س
- سداد قروض                     (20,000.00) ر.س
صافي التدفق من الأنشطة التمويلية    30,000.00 ر.س

صافي التغير في النقدية           (5,000.00) ر.س
النقدية في بداية الفترة           180,000.00 ر.س
النقدية في نهاية الفترة           175,000.00 ر.س";

            ShowReportContent("قائمة التدفقات النقدية", reportContent);
        }

        private void TrialBalanceButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"ميزان المراجعة كما في 31/12/2024

رقم الحساب    اسم الحساب              مدين          دائن
1001         النقدية                50,000.00      -
1002         البنك الأهلي           125,000.00      -
1003         العملاء               75,000.00      -
1004         المخزون               60,000.00      -
1005         المعدات              100,000.00      -
1006         الأثاث                40,000.00      -
2001         الموردون                  -       30,000.00
2002         مصروفات مستحقة            -       20,000.00
3001         رأس المال                 -      200,000.00
3002         الأرباح المحتجزة          -      200,000.00
4001         مبيعات                    -      175,000.00
4002         إيرادات أخرى              -       25,000.00
5001         تكلفة البضاعة المباعة   80,000.00      -
5002         مصروفات إدارية         35,000.00      -
5003         مصروفات تسويقية        20,000.00      -
5004         مصروفات أخرى           15,000.00      -

الإجماليات                      600,000.00   650,000.00

ملاحظة: يجب مراجعة الأرصدة للتأكد من توازن الميزان";

            ShowReportContent("ميزان المراجعة", reportContent);
        }

        private void AccountsReportButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"تقرير الحسابات المالية

إجمالي عدد الحسابات: 11 حساب

تصنيف الحسابات:
- الأصول: 6 حسابات
- الخصوم: 2 حساب
- حقوق الملكية: 1 حساب
- الإيرادات: 2 حساب
- المصروفات: 4 حسابات

الحسابات النشطة: 11 حساب
الحسابات غير النشطة: 0 حساب

آخر تحديث: " + DateTime.Now.ToString("yyyy-MM-dd HH:mm");

            ShowReportContent("تقرير الحسابات", reportContent);
        }

        private void JournalReportButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"تقرير القيود اليومية للفترة من 01/01/2024 إلى 31/12/2024

إجمالي عدد القيود: 8 قيود
القيود المؤكدة: 7 قيود
القيود المسودة: 1 قيد

إجمالي المبالغ: 387,000.00 ر.س

تفاصيل القيود:
JE001 - قيد افتتاحي - رأس المال - 200,000.00 ر.س
JE002 - شراء معدات مكتبية - 15,000.00 ر.س
JE003 - مبيعات نقدية - 25,000.00 ر.س
JE004 - دفع إيجار المكتب - 8,000.00 ر.س
JE005 - شراء بضاعة للمخزن - 50,000.00 ر.س
JE006 - مبيعات آجلة - 35,000.00 ر.س (مسودة)
JE007 - دفع رواتب الموظفين - 12,000.00 ر.س
JE008 - تحصيل من العملاء - 20,000.00 ر.س";

            ShowReportContent("تقرير القيود اليومية", reportContent);
        }

        private void InvoicesReportButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"تقرير الفواتير للفترة من 01/01/2024 إلى 31/12/2024

إجمالي عدد الفواتير: 8 فواتير

فواتير المبيعات: 5 فواتير
إجمالي المبيعات: 60,655.00 ر.س

فواتير المشتريات: 3 فواتير
إجمالي المشتريات: 40,250.00 ر.س

الفواتير المدفوعة: 5 فواتير
الفواتير المعلقة: 3 فواتير

أعلى فاتورة مبيعات: 28,750.00 ر.س (شركة البناء الحديث)
أعلى فاتورة مشتريات: 17,250.00 ر.س (مصنع الجودة)

معدل الفاتورة: 12,613.13 ر.س";

            ShowReportContent("تقرير الفواتير", reportContent);
        }

        private void CustomersReportButton_Click(object sender, RoutedEventArgs e)
        {
            var reportContent = @"تقرير العملاء والموردين

العملاء:
- شركة الأمل التجارية: 13,225.00 ر.س
- متجر الفجر: 6,325.00 ر.س
- شركة البناء الحديث: 28,750.00 ر.س
- مكتبة المعرفة: 3,680.00 ر.س
- مطعم الذواقة: 8,970.00 ر.س

إجمالي مبيعات العملاء: 60,950.00 ر.س

الموردون:
- مؤسسة النور للتوريدات: 9,200.00 ر.س
- مصنع الجودة: 17,250.00 ر.س
- شركة التقنية المتطورة: 13,800.00 ر.س

إجمالي مشتريات الموردين: 40,250.00 ر.س

أكبر عميل: شركة البناء الحديث (28,750.00 ر.س)
أكبر مورد: مصنع الجودة (17,250.00 ر.س)";

            ShowReportContent("تقرير العملاء والموردين", reportContent);
        }

        private void RefreshReportButton_Click(object sender, RoutedEventArgs e)
        {
            UpdateLastUpdateTime();
            MessageBox.Show("تم تحديث التقرير بنجاح", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ExportReportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير التقرير قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }
}
