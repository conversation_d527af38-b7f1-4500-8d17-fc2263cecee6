using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class InvoicesWindow : Window
    {
        private List<InvoiceItem> invoices;

        public InvoicesWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            invoices = new List<InvoiceItem>
            {
                new InvoiceItem { InvoiceNumber = "INV-2024-001", InvoiceType = "مبيعات", Date = "2024-01-15", CustomerSupplier = "شركة الأمل التجارية", TotalAmount = "11,500.00", TaxAmount = "1,725.00", NetAmount = "13,225.00", Status = "مدفوعة" },
                new InvoiceItem { InvoiceNumber = "INV-2024-002", InvoiceType = "مشتريات", Date = "2024-01-16", CustomerSupplier = "مؤسسة النور للتوريدات", TotalAmount = "8,000.00", TaxAmount = "1,200.00", NetAmount = "9,200.00", Status = "مدفوعة" },
                new InvoiceItem { InvoiceNumber = "INV-2024-003", InvoiceType = "مبيعات", Date = "2024-01-17", CustomerSupplier = "متجر الفجر", TotalAmount = "5,500.00", TaxAmount = "825.00", NetAmount = "6,325.00", Status = "معلقة" },
                new InvoiceItem { InvoiceNumber = "INV-2024-004", InvoiceType = "مبيعات", Date = "2024-01-18", CustomerSupplier = "شركة البناء الحديث", TotalAmount = "25,000.00", TaxAmount = "3,750.00", NetAmount = "28,750.00", Status = "مدفوعة" },
                new InvoiceItem { InvoiceNumber = "INV-2024-005", InvoiceType = "مشتريات", Date = "2024-01-19", CustomerSupplier = "مصنع الجودة", TotalAmount = "15,000.00", TaxAmount = "2,250.00", NetAmount = "17,250.00", Status = "معلقة" },
                new InvoiceItem { InvoiceNumber = "INV-2024-006", InvoiceType = "مبيعات", Date = "2024-01-20", CustomerSupplier = "مكتبة المعرفة", TotalAmount = "3,200.00", TaxAmount = "480.00", NetAmount = "3,680.00", Status = "مدفوعة" },
                new InvoiceItem { InvoiceNumber = "INV-2024-007", InvoiceType = "مشتريات", Date = "2024-01-21", CustomerSupplier = "شركة التقنية المتطورة", TotalAmount = "12,000.00", TaxAmount = "1,800.00", NetAmount = "13,800.00", Status = "مدفوعة" },
                new InvoiceItem { InvoiceNumber = "INV-2024-008", InvoiceType = "مبيعات", Date = "2024-01-22", CustomerSupplier = "مطعم الذواقة", TotalAmount = "7,800.00", TaxAmount = "1,170.00", NetAmount = "8,970.00", Status = "معلقة" }
            };

            InvoicesDataGrid.ItemsSource = invoices;
            UpdateStatusAndTotals();
        }

        private void UpdateStatusAndTotals()
        {
            StatusLabel.Text = $"جاهز - {invoices.Count} فاتورة";
            
            var salesInvoices = invoices.Where(i => i.InvoiceType == "مبيعات").ToList();
            var purchaseInvoices = invoices.Where(i => i.InvoiceType == "مشتريات").ToList();
            
            decimal totalSales = 0;
            decimal totalPurchases = 0;
            
            foreach (var invoice in salesInvoices)
            {
                if (decimal.TryParse(invoice.NetAmount.Replace(",", ""), out decimal amount))
                    totalSales += amount;
            }
            
            foreach (var invoice in purchaseInvoices)
            {
                if (decimal.TryParse(invoice.NetAmount.Replace(",", ""), out decimal amount))
                    totalPurchases += amount;
            }
            
            TotalSalesLabel.Text = $"{totalSales:N2} ر.س";
            TotalPurchasesLabel.Text = $"{totalPurchases:N2} ر.س";
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox.Text == "البحث في الفواتير...")
            {
                textBox.Text = "";
                textBox.Foreground = Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = "البحث في الفواتير...";
                textBox.Foreground = Brushes.Gray;
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطبيق البحث قريباً", "البحث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddSalesInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddInvoiceWindow("مبيعات");
            if (addInvoiceWindow.ShowDialog() == true)
            {
                LoadSampleData();
            }
        }

        private void AddPurchaseInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddInvoiceWindow("مشتريات");
            if (addInvoiceWindow.ShowDialog() == true)
            {
                LoadSampleData();
            }
        }

        private void ViewInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var invoice = button?.DataContext as InvoiceItem;
            if (invoice != null)
            {
                MessageBox.Show($"عرض تفاصيل الفاتورة: {invoice.InvoiceNumber}\nالنوع: {invoice.InvoiceType}\nالتاريخ: {invoice.Date}\nالعميل/المورد: {invoice.CustomerSupplier}\nالمبلغ الصافي: {invoice.NetAmount}", 
                               "تفاصيل الفاتورة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void EditInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var invoice = button?.DataContext as InvoiceItem;
            if (invoice != null)
            {
                MessageBox.Show($"تعديل الفاتورة: {invoice.InvoiceNumber}", "تعديل فاتورة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var invoice = button?.DataContext as InvoiceItem;
            if (invoice != null)
            {
                var result = MessageBox.Show($"هل تريد حذف الفاتورة: {invoice.InvoiceNumber}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                if (result == MessageBoxResult.Yes)
                {
                    invoices.Remove(invoice);
                    InvoicesDataGrid.Items.Refresh();
                    UpdateStatusAndTotals();
                }
            }
        }

        private void PrintInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var invoice = button?.DataContext as InvoiceItem;
            if (invoice != null)
            {
                MessageBox.Show($"طباعة الفاتورة: {invoice.InvoiceNumber}", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PaymentButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var invoice = button?.DataContext as InvoiceItem;
            if (invoice != null)
            {
                MessageBox.Show($"تسجيل دفعة للفاتورة: {invoice.InvoiceNumber}", "تسجيل دفعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير البيانات قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class InvoiceItem
    {
        public string InvoiceNumber { get; set; }
        public string InvoiceType { get; set; }
        public string Date { get; set; }
        public string CustomerSupplier { get; set; }
        public string TotalAmount { get; set; }
        public string TaxAmount { get; set; }
        public string NetAmount { get; set; }
        public string Status { get; set; }
    }
}
