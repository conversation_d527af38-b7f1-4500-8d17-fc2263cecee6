<Window x:Class="AccountingSystem.Views.NumberSettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إعدادات الأرقام - نظام المحاسبة" 
        Height="350" Width="500"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#3F51B5">
            <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                <TextBlock Text="🔢" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock Text="إعدادات الأرقام" 
                           FontSize="20" FontWeight="Bold"
                           Foreground="White" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- Main Content -->
        <StackPanel Grid.Row="1" Margin="40,30" VerticalAlignment="Center">
            
            <TextBlock Text="اختر نوع الأرقام المطلوب عرضها في النظام:" 
                       FontSize="16" FontWeight="Bold" 
                       Margin="0,0,0,30" TextAlignment="Center"/>
            
            <!-- Arabic Numbers Option -->
            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="2" 
                    CornerRadius="10" Padding="20" Margin="0,0,0,15">
                <StackPanel>
                    <RadioButton x:Name="ArabicNumbersRadio" 
                                 GroupName="NumberType"
                                 FontSize="16" FontWeight="Bold"
                                 Margin="0,0,0,10">
                        <TextBlock Text="الأرقام العربية" FontSize="16" FontWeight="Bold"/>
                    </RadioButton>
                    <TextBlock Text="مثال: ١٢٣٤٥٦٧٨٩٠" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="#2E7D32" Margin="25,0,0,5"/>
                    <TextBlock Text="المبلغ: ١٬٢٣٤٫٥٦ ريال" 
                               FontSize="14" Foreground="#666" Margin="25,0,0,0"/>
                </StackPanel>
            </Border>
            
            <!-- Hindi Numbers Option -->
            <Border Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="2" 
                    CornerRadius="10" Padding="20">
                <StackPanel>
                    <RadioButton x:Name="HindiNumbersRadio" 
                                 GroupName="NumberType"
                                 FontSize="16" FontWeight="Bold"
                                 Margin="0,0,0,10"
                                 IsChecked="True">
                        <TextBlock Text="الأرقام الهندية (الإنجليزية)" FontSize="16" FontWeight="Bold"/>
                    </RadioButton>
                    <TextBlock Text="مثال: 1234567890" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="#2E7D32" Margin="25,0,0,5"/>
                    <TextBlock Text="المبلغ: 1,234.56 ريال" 
                               FontSize="14" Foreground="#666" Margin="25,0,0,0"/>
                </StackPanel>
            </Border>
            
        </StackPanel>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ الإعدادات" 
                        Width="140" Height="40" FontSize="14" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White" Margin="10,0"
                        Click="SaveButton_Click"/>
                <Button Content="❌ إلغاء" 
                        Width="140" Height="40" FontSize="14"
                        Background="#F44336" Foreground="White" Margin="10,0"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
