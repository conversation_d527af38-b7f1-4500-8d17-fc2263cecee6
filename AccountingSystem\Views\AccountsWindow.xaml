<Window x:Class="AccountingSystem.Views.AccountsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="دليل الحسابات - نظام المحاسبة المتقدم" 
        Height="600" Width="900"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="📋" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="دليل الحسابات" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="➕ حساب جديد" 
                            Background="#4CAF50" Foreground="White"
                            Width="120" Height="35" Margin="5,0"
                            Click="AddAccountButton_Click"/>
                    <Button Content="إغلاق" 
                            Background="#F44336" Foreground="White"
                            Width="80" Height="35" Margin="5,0"
                            Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Search and Filter -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>
                
                <TextBox Grid.Column="0" x:Name="SearchTextBox" 
                         Height="35" FontSize="14" Padding="10"
                         Background="#F9F9F9"
                         Text="البحث في الحسابات..." 
                         Foreground="#999"
                         GotFocus="SearchTextBox_GotFocus"
                         LostFocus="SearchTextBox_LostFocus"/>
                
                <ComboBox Grid.Column="1" x:Name="AccountTypeComboBox"
                          Height="35" FontSize="14" Margin="10,0,0,0"
                          Background="#F9F9F9">
                    <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                    <ComboBoxItem Content="الأصول"/>
                    <ComboBoxItem Content="الخصوم"/>
                    <ComboBoxItem Content="حقوق الملكية"/>
                    <ComboBoxItem Content="الإيرادات"/>
                    <ComboBoxItem Content="المصروفات"/>
                </ComboBox>
                
                <Button Grid.Column="2" Content="🔍 بحث"
                        Height="35" FontSize="14" Margin="10,0,0,0"
                        Background="#2196F3" Foreground="White"
                        Click="SearchButton_Click"/>
            </Grid>
            
            <!-- Accounts List -->
            <Border Grid.Row="1" BorderBrush="#DDD" BorderThickness="1" CornerRadius="5">
                <DataGrid x:Name="AccountsDataGrid" 
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          FontSize="13">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الحساب" Binding="{Binding AccountNumber}" Width="120"/>
                        <DataGridTextColumn Header="اسم الحساب" Binding="{Binding AccountName}" Width="*"/>
                        <DataGridTextColumn Header="نوع الحساب" Binding="{Binding AccountType}" Width="150"/>
                        <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance}" Width="120"/>
                        <DataGridTextColumn Header="العملة" Binding="{Binding Currency}" Width="80"/>
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="✏️" Width="30" Height="25" Margin="2"
                                                Background="#FF9800" Foreground="White"
                                                Click="EditAccountButton_Click"
                                                ToolTip="تعديل"/>
                                        <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                                Background="#F44336" Foreground="White"
                                                Click="DeleteAccountButton_Click"
                                                ToolTip="حذف"/>
                                        <Button Content="📊" Width="30" Height="25" Margin="2"
                                                Background="#4CAF50" Foreground="White"
                                                Click="ViewAccountButton_Click"
                                                ToolTip="عرض"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>
        </Grid>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" x:Name="StatusLabel"
                           Text="جاهز - 0 حساب"
                           VerticalAlignment="Center" Margin="10,0"
                           FontSize="12" Foreground="#666"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="📤 تصدير" Width="80" Height="30" Margin="5,0"
                            Background="#607D8B" Foreground="White"
                            Click="ExportButton_Click"/>
                    <Button Content="📥 استيراد" Width="80" Height="30" Margin="5,0"
                            Background="#795548" Foreground="White"
                            Click="ImportButton_Click"/>
                    <Button Content="🔄 تحديث" Width="80" Height="30" Margin="5,0"
                            Background="#9C27B0" Foreground="White"
                            Click="RefreshButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
