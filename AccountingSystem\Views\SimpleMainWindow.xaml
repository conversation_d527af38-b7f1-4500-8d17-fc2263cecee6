<Window x:Class="AccountingSystem.Views.SimpleMainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="نظام المحاسبة المتقدم" 
        Height="700" Width="1000"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="30"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="💼" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="نظام المحاسبة المتقدم" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="تسجيل الخروج" 
                            Background="Red" Foreground="White"
                            Width="100" Height="35"
                            Click="LogoutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <!-- Side Menu -->
            <Border Grid.Column="0" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,0,1,0">
                <ScrollViewer VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="10">
                        
                        <!-- Dashboard -->
                        <Button x:Name="DashboardButton" 
                                Content="📊 لوحة التحكم"
                                Height="45" FontSize="14"
                                Background="#E3F2FD" Foreground="#1976D2"
                                HorizontalAlignment="Stretch"
                                Margin="0,5"
                                Click="DashboardButton_Click"/>
                        
                        <!-- Accounts Section -->
                        <TextBlock Text="الحسابات" FontWeight="Bold" Margin="0,15,0,5" Foreground="#666"/>
                        
                        <Button Content="📋 دليل الحسابات"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="AccountsButton_Click"/>
                        
                        <Button Content="➕ إضافة حساب"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="AddAccountButton_Click"/>
                        
                        <!-- Journal Entries Section -->
                        <TextBlock Text="القيود اليومية" FontWeight="Bold" Margin="0,15,0,5" Foreground="#666"/>
                        
                        <Button Content="📝 قيد جديد"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="AddJournalEntryButton_Click"/>
                        
                        <Button Content="📚 عرض القيود"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="JournalEntriesButton_Click"/>
                        
                        <!-- Invoices Section -->
                        <TextBlock Text="الفواتير" FontWeight="Bold" Margin="0,15,0,5" Foreground="#666"/>
                        
                        <Button Content="🧾 فاتورة مبيعات"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="AddSalesInvoiceButton_Click"/>
                        
                        <Button Content="🛒 فاتورة مشتريات"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="AddPurchaseInvoiceButton_Click"/>
                        
                        <Button Content="📄 عرض الفواتير"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="InvoicesButton_Click"/>
                        
                        <!-- Reports Section -->
                        <TextBlock Text="التقارير" FontWeight="Bold" Margin="0,15,0,5" Foreground="#666"/>
                        
                        <Button Content="📈 جميع التقارير"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="ReportsButton_Click"/>
                        
                        <Button Content="💰 قائمة الدخل"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="IncomeStatementButton_Click"/>
                        
                        <Button Content="⚖️ الميزانية العمومية"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="BalanceSheetButton_Click"/>
                        
                        <!-- Inventory Management -->
                        <TextBlock Text="إدارة المخزون" FontWeight="Bold" Margin="0,15,0,5" Foreground="#666"/>

                        <Button Content="📦 إدارة الأصناف"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="ItemsButton_Click"/>

                        <Button Content="🏭 إدارة الموردين"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="SuppliersButton_Click"/>

                        <!-- Settings -->
                        <TextBlock Text="الإعدادات" FontWeight="Bold" Margin="0,15,0,5" Foreground="#666"/>

                        <Button Content="⚙️ إعدادات النظام"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="SettingsButton_Click"/>

                        <Button Content="👥 إدارة المستخدمين"
                                Height="40" FontSize="13"
                                Background="White" Foreground="#333"
                                HorizontalAlignment="Stretch"
                                Margin="0,2"
                                Click="UserManagementButton_Click"/>
                        
                    </StackPanel>
                </ScrollViewer>
            </Border>
            
            <!-- Main Content Area -->
            <Grid Grid.Column="1" x:Name="MainContentArea" Background="White">
                <!-- Dashboard Content (Default) -->
                <Grid x:Name="DashboardContent" Visibility="Visible">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="30">
                            <TextBlock Text="لوحة التحكم" FontSize="28" FontWeight="Bold" Margin="0,0,0,20" Foreground="#2196F3"/>
                            
                            <!-- Statistics Cards -->
                            <Grid Margin="0,0,0,30">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <!-- Total Revenue Card -->
                                <Border Grid.Column="0" Background="#4CAF50" CornerRadius="8" Margin="5" Padding="20">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="💰" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBlock Text="إجمالي الإيرادات" FontSize="14" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="0.00 ر.س" FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                
                                <!-- Total Expenses Card -->
                                <Border Grid.Column="1" Background="#F44336" CornerRadius="8" Margin="5" Padding="20">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="💸" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBlock Text="إجمالي المصروفات" FontSize="14" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="0.00 ر.س" FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                
                                <!-- Net Profit Card -->
                                <Border Grid.Column="2" Background="#2196F3" CornerRadius="8" Margin="5" Padding="20">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="📊" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBlock Text="صافي الربح" FontSize="14" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="0.00 ر.س" FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                                
                                <!-- Total Accounts Card -->
                                <Border Grid.Column="3" Background="#FF9800" CornerRadius="8" Margin="5" Padding="20">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="📋" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                        <TextBlock Text="عدد الحسابات" FontSize="14" Foreground="White" HorizontalAlignment="Center"/>
                                        <TextBlock Text="0" FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                            
                            <!-- Recent Activities -->
                            <Border Background="#F9F9F9" CornerRadius="8" Padding="20">
                                <StackPanel>
                                    <TextBlock Text="الأنشطة الأخيرة" FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                                    <TextBlock Text="لا توجد أنشطة حديثة" 
                                             HorizontalAlignment="Center" 
                                             Margin="0,20" 
                                             Foreground="#666"
                                             FontSize="14"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
                
                <!-- Other Content Areas (Hidden by default) -->
                <Grid x:Name="AccountsContent" Visibility="Collapsed">
                    <TextBlock Text="دليل الحسابات" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Grid>
                
                <Grid x:Name="JournalEntriesContent" Visibility="Collapsed">
                    <TextBlock Text="القيود اليومية" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Grid>
                
                <Grid x:Name="InvoicesContent" Visibility="Collapsed">
                    <TextBlock Text="الفواتير" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Grid>
                
                <Grid x:Name="ReportsContent" Visibility="Collapsed">
                    <TextBlock Text="التقارير" FontSize="24" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Grid>
                
            </Grid>
        </Grid>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <TextBlock Text="© 2024 نظام المحاسبة المتقدم - جميع الحقوق محفوظة" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"
                       FontSize="12" 
                       Foreground="#666"/>
        </Border>
    </Grid>
</Window>
