<Window x:Class="AccountingSystem.Views.ItemsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الأصناف - نظام المحاسبة المتقدم" 
        Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#9C27B0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="📦" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة الأصناف والمنتجات" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="➕ صنف جديد" 
                            Background="#4CAF50" Foreground="White"
                            Width="120" Height="35" Margin="5,0"
                            Click="AddItemButton_Click"/>
                    <Button Content="📊 تقرير المخزون" 
                            Background="#FF9800" Foreground="White"
                            Width="140" Height="35" Margin="5,0"
                            Click="InventoryReportButton_Click"/>
                    <Button Content="إغلاق" 
                            Background="#F44336" Foreground="White"
                            Width="80" Height="35" Margin="5,0"
                            Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Search and Filter -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="الفئة:" FontSize="12" Margin="0,0,0,5"/>
                    <ComboBox x:Name="CategoryComboBox" Height="35" FontSize="14">
                        <ComboBoxItem Content="جميع الفئات" IsSelected="True"/>
                        <ComboBoxItem Content="إلكترونيات"/>
                        <ComboBoxItem Content="ملابس"/>
                        <ComboBoxItem Content="أغذية"/>
                        <ComboBoxItem Content="مكتبية"/>
                        <ComboBoxItem Content="أدوات منزلية"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="الحالة:" FontSize="12" Margin="0,0,0,5"/>
                    <ComboBox x:Name="StatusComboBox" Height="35" FontSize="14">
                        <ComboBoxItem Content="جميع الحالات" IsSelected="True"/>
                        <ComboBoxItem Content="متوفر"/>
                        <ComboBoxItem Content="نفد المخزون"/>
                        <ComboBoxItem Content="غير نشط"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="البحث (اسم أو باركود):" FontSize="12" Margin="0,0,0,5"/>
                    <TextBox x:Name="SearchTextBox" Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" Text="البحث في الأصناف أو مسح الباركود..." 
                             Foreground="#999"
                             GotFocus="SearchTextBox_GotFocus"
                             LostFocus="SearchTextBox_LostFocus"
                             KeyDown="SearchTextBox_KeyDown"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" Margin="10,0,0,0">
                    <TextBlock Text="مسح الباركود:" FontSize="12" Margin="0,0,0,5"/>
                    <Button Content="📷 تشغيل الماسح" Height="35" FontSize="12"
                            Background="#2196F3" Foreground="White"
                            Click="StartBarcodeScanner_Click"/>
                </StackPanel>
                
                <Button Grid.Column="4" Content="🔍 بحث"
                        Height="35" FontSize="14" Margin="10,25,0,0"
                        Background="#9C27B0" Foreground="White"
                        Click="SearchButton_Click"/>
            </Grid>
            
            <!-- Items List -->
            <Border Grid.Row="1" BorderBrush="#DDD" BorderThickness="1" CornerRadius="5">
                <DataGrid x:Name="ItemsDataGrid" 
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          FontSize="13">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الباركود" Binding="{Binding Barcode}" Width="120"/>
                        <DataGridTextColumn Header="اسم الصنف" Binding="{Binding ItemName}" Width="*"/>
                        <DataGridTextColumn Header="الفئة" Binding="{Binding Category}" Width="100"/>
                        <DataGridTextColumn Header="الوحدة" Binding="{Binding Unit}" Width="80"/>
                        <DataGridTextColumn Header="سعر الشراء" Binding="{Binding PurchasePrice}" Width="100"/>
                        <DataGridTextColumn Header="سعر البيع" Binding="{Binding SalePrice}" Width="100"/>
                        <DataGridTextColumn Header="الكمية المتاحة" Binding="{Binding AvailableQuantity}" Width="120"/>
                        <DataGridTextColumn Header="الحد الأدنى" Binding="{Binding MinimumStock}" Width="100"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="👁️" Width="30" Height="25" Margin="2"
                                                Background="#2196F3" Foreground="White"
                                                Click="ViewItemButton_Click"
                                                ToolTip="عرض"/>
                                        <Button Content="✏️" Width="30" Height="25" Margin="2"
                                                Background="#FF9800" Foreground="White"
                                                Click="EditItemButton_Click"
                                                ToolTip="تعديل"/>
                                        <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                                Background="#F44336" Foreground="White"
                                                Click="DeleteItemButton_Click"
                                                ToolTip="حذف"/>
                                        <Button Content="📦" Width="30" Height="25" Margin="2"
                                                Background="#4CAF50" Foreground="White"
                                                Click="StockAdjustmentButton_Click"
                                                ToolTip="تعديل المخزون"/>
                                        <Button Content="🏷️" Width="30" Height="25" Margin="2"
                                                Background="#9C27B0" Foreground="White"
                                                Click="PrintBarcodeButton_Click"
                                                ToolTip="طباعة باركود"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>
        </Grid>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <TextBlock x:Name="StatusLabel" Text="جاهز - 0 صنف" FontSize="12" Foreground="#666" Margin="0,0,20,0"/>
                    <TextBlock Text="إجمالي قيمة المخزون:" FontSize="12" Margin="0,0,5,0"/>
                    <TextBlock x:Name="TotalInventoryValueLabel" Text="0.00 ر.س" FontWeight="Bold" Foreground="#9C27B0" Margin="0,0,20,0"/>
                    <TextBlock Text="أصناف نفد مخزونها:" FontSize="12" Margin="0,0,5,0"/>
                    <TextBlock x:Name="OutOfStockLabel" Text="0" FontWeight="Bold" Foreground="#F44336"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="📤 تصدير" Width="80" Height="30" Margin="5,0"
                            Background="#607D8B" Foreground="White"
                            Click="ExportButton_Click"/>
                    <Button Content="📥 استيراد" Width="80" Height="30" Margin="5,0"
                            Background="#795548" Foreground="White"
                            Click="ImportButton_Click"/>
                    <Button Content="🔄 تحديث" Width="80" Height="30" Margin="5,0"
                            Background="#9C27B0" Foreground="White"
                            Click="RefreshButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
