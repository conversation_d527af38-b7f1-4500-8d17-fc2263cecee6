<Window x:Class="AccountingSystem.Views.InvoicesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="الفواتير - نظام المحاسبة المتقدم" 
        Height="650" Width="1100"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="50"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#E91E63">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="📄" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إدارة الفواتير" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="🧾 فاتورة مبيعات" 
                            Background="#4CAF50" Foreground="White"
                            Width="140" Height="35" Margin="5,0"
                            Click="AddSalesInvoiceButton_Click"/>
                    <Button Content="🛒 فاتورة مشتريات" 
                            Background="#FF9800" Foreground="White"
                            Width="140" Height="35" Margin="5,0"
                            Click="AddPurchaseInvoiceButton_Click"/>
                    <Button Content="إغلاق" 
                            Background="#F44336" Foreground="White"
                            Width="80" Height="35" Margin="5,0"
                            Click="CloseButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Filter and Search -->
            <Grid Grid.Row="0" Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="نوع الفاتورة:" FontSize="12" Margin="0,0,0,5"/>
                    <ComboBox x:Name="InvoiceTypeComboBox" Height="35" FontSize="14">
                        <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                        <ComboBoxItem Content="مبيعات"/>
                        <ComboBoxItem Content="مشتريات"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                    <TextBlock Text="من تاريخ:" FontSize="12" Margin="0,0,0,5"/>
                    <TextBox x:Name="FromDateTextBox" Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" Text="01/01/2024"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="إلى تاريخ:" FontSize="12" Margin="0,0,0,5"/>
                    <TextBox x:Name="ToDateTextBox" Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" Text="31/12/2024"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" Margin="10,0,0,0">
                    <TextBlock Text="البحث:" FontSize="12" Margin="0,0,0,5"/>
                    <TextBox x:Name="SearchTextBox" Height="35" FontSize="14" Padding="10"
                             Background="#F9F9F9" Text="البحث في الفواتير..." 
                             Foreground="#999"
                             GotFocus="SearchTextBox_GotFocus"
                             LostFocus="SearchTextBox_LostFocus"/>
                </StackPanel>
                
                <Button Grid.Column="4" Content="🔍 بحث"
                        Height="35" FontSize="14" Margin="10,25,0,0"
                        Background="#E91E63" Foreground="White"
                        Click="SearchButton_Click"/>
            </Grid>
            
            <!-- Invoices List -->
            <Border Grid.Row="1" BorderBrush="#DDD" BorderThickness="1" CornerRadius="5">
                <DataGrid x:Name="InvoicesDataGrid" 
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          FontSize="13">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                        <DataGridTextColumn Header="النوع" Binding="{Binding InvoiceType}" Width="80"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date}" Width="100"/>
                        <DataGridTextColumn Header="العميل/المورد" Binding="{Binding CustomerSupplier}" Width="*"/>
                        <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding TotalAmount}" Width="120"/>
                        <DataGridTextColumn Header="الضريبة" Binding="{Binding TaxAmount}" Width="100"/>
                        <DataGridTextColumn Header="الصافي" Binding="{Binding NetAmount}" Width="120"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="80"/>
                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="👁️" Width="30" Height="25" Margin="2"
                                                Background="#2196F3" Foreground="White"
                                                Click="ViewInvoiceButton_Click"
                                                ToolTip="عرض"/>
                                        <Button Content="✏️" Width="30" Height="25" Margin="2"
                                                Background="#FF9800" Foreground="White"
                                                Click="EditInvoiceButton_Click"
                                                ToolTip="تعديل"/>
                                        <Button Content="🗑️" Width="30" Height="25" Margin="2"
                                                Background="#F44336" Foreground="White"
                                                Click="DeleteInvoiceButton_Click"
                                                ToolTip="حذف"/>
                                        <Button Content="📄" Width="30" Height="25" Margin="2"
                                                Background="#4CAF50" Foreground="White"
                                                Click="PrintInvoiceButton_Click"
                                                ToolTip="طباعة"/>
                                        <Button Content="💰" Width="30" Height="25" Margin="2"
                                                Background="#9C27B0" Foreground="White"
                                                Click="PaymentButton_Click"
                                                ToolTip="دفع"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Border>
        </Grid>
        
        <!-- Footer -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="10,0">
                    <TextBlock x:Name="StatusLabel" Text="جاهز - 0 فاتورة" FontSize="12" Foreground="#666" Margin="0,0,20,0"/>
                    <TextBlock Text="إجمالي المبيعات:" FontSize="12" Margin="0,0,5,0"/>
                    <TextBlock x:Name="TotalSalesLabel" Text="0.00 ر.س" FontWeight="Bold" Foreground="#4CAF50" Margin="0,0,20,0"/>
                    <TextBlock Text="إجمالي المشتريات:" FontSize="12" Margin="0,0,5,0"/>
                    <TextBlock x:Name="TotalPurchasesLabel" Text="0.00 ر.س" FontWeight="Bold" Foreground="#FF9800"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="10,0">
                    <Button Content="📤 تصدير" Width="80" Height="30" Margin="5,0"
                            Background="#607D8B" Foreground="White"
                            Click="ExportButton_Click"/>
                    <Button Content="🔄 تحديث" Width="80" Height="30" Margin="5,0"
                            Background="#9C27B0" Foreground="White"
                            Click="RefreshButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
