<Window x:Class="AccountingSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="تسجيل الدخول - نظام المحاسبة" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        FlowDirection="RightToLeft"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">
    
    <Border CornerRadius="15" Background="White" 
            BorderBrush="{DynamicResource MaterialDesignDivider}" 
            BorderThickness="1">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="315" ShadowDepth="5" Opacity="0.3" BlurRadius="10"/>
        </Border.Effect>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            
            <!-- Header -->
            <Grid Grid.Row="0" Background="{DynamicResource MaterialDesignPaper}" 
                  Height="80">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="نظام المحاسبة المتقدم" 
                               FontSize="18" FontWeight="Bold"
                               Foreground="{DynamicResource MaterialDesignBody}"/>
                    <TextBlock Text="تسجيل الدخول" 
                               FontSize="14" 
                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
                
                <Button Grid.Column="1" 
                        Style="{StaticResource MaterialDesignIconButton}"
                        Width="30" Height="30" 
                        Margin="0,0,10,0"
                        Click="CloseButton_Click">
                    <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                </Button>
            </Grid>
            
            <!-- Content -->
            <StackPanel Grid.Row="1" Margin="40,30">
                <!-- Logo/Icon -->
                <Ellipse Width="80" Height="80"
                         Fill="LightBlue"
                         HorizontalAlignment="Center"
                         Margin="0,0,0,30"/>

                <!-- Username Label -->
                <TextBlock Text="اسم المستخدم:"
                           FontSize="14"
                           Margin="0,0,0,5"
                           HorizontalAlignment="Right"/>

                <!-- Username -->
                <TextBox x:Name="UsernameTextBox"
                         Text="{Binding Username, UpdateSourceTrigger=PropertyChanged}"
                         Margin="0,0,0,20"
                         FontSize="14"
                         Height="35"
                         Padding="10,5"
                         KeyDown="UsernameTextBox_KeyDown">
                </TextBox>

                <!-- Password Label -->
                <TextBlock Text="كلمة المرور:"
                           FontSize="14"
                           Margin="0,0,0,5"
                           HorizontalAlignment="Right"/>

                <!-- Password -->
                <PasswordBox x:Name="PasswordBox"
                             Margin="0,0,0,20"
                             FontSize="14"
                             Height="35"
                             Padding="10,5"
                             PasswordChanged="PasswordBox_PasswordChanged"
                             KeyDown="PasswordBox_KeyDown">
                </PasswordBox>

                <!-- Remember Me -->
                <CheckBox Content="تذكرني"
                          IsChecked="{Binding RememberMe}"
                          Margin="0,0,0,20"
                          HorizontalAlignment="Right"/>

                <!-- Login Button -->
                <Button x:Name="LoginButton"
                        Content="تسجيل الدخول"
                        Height="50"
                        Width="200"
                        FontSize="16"
                        FontWeight="Bold"
                        Margin="0,10,0,15"
                        Background="Blue"
                        Foreground="White"
                        HorizontalAlignment="Center"
                        Click="LoginButton_Click"
                        IsDefault="True">
                </Button>

                <!-- Simple Forgot Password -->
                <Button Content="نسيت كلمة المرور؟"
                        Background="Transparent"
                        Foreground="Gray"
                        BorderThickness="0"
                        HorizontalAlignment="Center"
                        Click="ForgotPassword_Click"
                        Cursor="Hand"/>

                <!-- Error Message -->
                <Border Background="Red"
                        CornerRadius="4"
                        Padding="10"
                        Margin="0,15,0,0"
                        x:Name="ErrorBorder"
                        Visibility="Collapsed">
                    <TextBlock x:Name="ErrorText"
                               Foreground="White"
                               TextWrapping="Wrap"
                               HorizontalAlignment="Center"/>
                </Border>

            </StackPanel>
            
            <!-- Footer -->
            <Grid Grid.Row="2" Background="{DynamicResource MaterialDesignCardBackground}" 
                  Height="50">
                <StackPanel Orientation="Horizontal" 
                            HorizontalAlignment="Center" 
                            VerticalAlignment="Center">
                    <TextBlock Text="© 2024 نظام المحاسبة المتقدم" 
                               FontSize="12" 
                               Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>
            </Grid>
            
            <!-- Loading Indicator -->
            <Grid Grid.Row="0" Grid.RowSpan="3" 
                  Background="#80000000" 
                  Visibility="{Binding IsLoading, Converter={StaticResource BoolToVisibilityConverter}}">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                 Width="50" Height="50"
                                 IsIndeterminate="True"/>
                    <TextBlock Text="جاري تسجيل الدخول..." 
                               Margin="0,10,0,0" 
                               HorizontalAlignment="Center"
                               Foreground="White"
                               FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
