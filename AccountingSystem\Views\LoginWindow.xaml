<Window x:Class="AccountingSystem.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تسجيل الدخول - نظام المحاسبة"
        Height="400" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize"
        FlowDirection="RightToLeft"
        Background="White">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Height="60">
            <Grid>
                <TextBlock Text="نظام المحاسبة المتقدم - تسجيل الدخول"
                           FontSize="18" FontWeight="Bold"
                           Foreground="White"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"/>
                <Button Content="✕"
                        Width="30" Height="30"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center"
                        Margin="10,0,0,0"
                        Background="Transparent"
                        Foreground="White"
                        BorderThickness="0"
                        FontSize="16"
                        Click="CloseButton_Click"/>
            </Grid>
        </Border>

        <!-- Content -->
        <StackPanel Grid.Row="1" Margin="50,40" HorizontalAlignment="Center">

            <!-- Title -->
            <TextBlock Text="تسجيل الدخول"
                       FontSize="24"
                       FontWeight="Bold"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,30"
                       Foreground="#2196F3"/>

            <!-- Username -->
            <TextBlock Text="اسم المستخدم:"
                       FontSize="16"
                       Margin="0,0,0,8"
                       HorizontalAlignment="Right"/>

            <TextBox x:Name="UsernameTextBox"
                     Width="300"
                     Height="40"
                     FontSize="16"
                     Padding="10"
                     Margin="0,0,0,20"
                     Text="admin"
                     TabIndex="1"/>

            <!-- Password -->
            <TextBlock Text="كلمة المرور:"
                       FontSize="16"
                       Margin="0,0,0,8"
                       HorizontalAlignment="Right"/>

            <PasswordBox x:Name="PasswordBox"
                         Width="300"
                         Height="40"
                         FontSize="16"
                         Padding="10"
                         Margin="0,0,0,30"
                         TabIndex="2"/>

            <!-- Login Button -->
            <Button x:Name="LoginButton"
                    Content="تسجيل الدخول"
                    Width="300"
                    Height="50"
                    FontSize="18"
                    FontWeight="Bold"
                    Background="#2196F3"
                    Foreground="White"
                    BorderThickness="0"
                    Margin="0,0,0,20"
                    Click="LoginButton_Click"
                    TabIndex="3"
                    IsDefault="True"/>

            <!-- Error Message -->
            <TextBlock x:Name="ErrorMessage"
                       Foreground="Red"
                       FontSize="14"
                       HorizontalAlignment="Center"
                       TextWrapping="Wrap"
                       Visibility="Collapsed"
                       Margin="0,10,0,0"/>

            <!-- Success Message -->
            <TextBlock x:Name="SuccessMessage"
                       Foreground="Green"
                       FontSize="14"
                       HorizontalAlignment="Center"
                       TextWrapping="Wrap"
                       Visibility="Collapsed"
                       Margin="0,10,0,0"/>

        </StackPanel>
    </Grid>
</Window>
