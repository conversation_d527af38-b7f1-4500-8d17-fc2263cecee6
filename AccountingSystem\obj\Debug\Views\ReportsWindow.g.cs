﻿#pragma checksum "..\..\..\Views\ReportsWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "E1F3F6D4E5429A375CD7D58BDB6FF91CA9AA3625A3C45319229AD75AD6F9C0A3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem.Views {
    
    
    /// <summary>
    /// ReportsWindow
    /// </summary>
    public partial class ReportsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 60 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button IncomeStatementButton;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BalanceSheetButton;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CashFlowButton;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TrialBalanceButton;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AccountsReportButton;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button JournalReportButton;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InvoicesReportButton;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CustomersReportButton;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FromDateTextBox;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ToDateTextBox;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid WelcomeContent;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ReportScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReportContent;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusLabel;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\Views\ReportsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateLabel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem;component/views/reportswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\ReportsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 36 "..\..\..\Views\ReportsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 40 "..\..\..\Views\ReportsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.IncomeStatementButton = ((System.Windows.Controls.Button)(target));
            
            #line 66 "..\..\..\Views\ReportsWindow.xaml"
            this.IncomeStatementButton.Click += new System.Windows.RoutedEventHandler(this.IncomeStatementButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BalanceSheetButton = ((System.Windows.Controls.Button)(target));
            
            #line 74 "..\..\..\Views\ReportsWindow.xaml"
            this.BalanceSheetButton.Click += new System.Windows.RoutedEventHandler(this.BalanceSheetButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CashFlowButton = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\Views\ReportsWindow.xaml"
            this.CashFlowButton.Click += new System.Windows.RoutedEventHandler(this.CashFlowButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TrialBalanceButton = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\Views\ReportsWindow.xaml"
            this.TrialBalanceButton.Click += new System.Windows.RoutedEventHandler(this.TrialBalanceButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.AccountsReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 101 "..\..\..\Views\ReportsWindow.xaml"
            this.AccountsReportButton.Click += new System.Windows.RoutedEventHandler(this.AccountsReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.JournalReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 109 "..\..\..\Views\ReportsWindow.xaml"
            this.JournalReportButton.Click += new System.Windows.RoutedEventHandler(this.JournalReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.InvoicesReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\Views\ReportsWindow.xaml"
            this.InvoicesReportButton.Click += new System.Windows.RoutedEventHandler(this.InvoicesReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CustomersReportButton = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\Views\ReportsWindow.xaml"
            this.CustomersReportButton.Click += new System.Windows.RoutedEventHandler(this.CustomersReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.FromDateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.ToDateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            
            #line 145 "..\..\..\Views\ReportsWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RefreshReportButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.WelcomeContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 15:
            this.ReportScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 16:
            this.ReportContent = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.StatusLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.LastUpdateLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

