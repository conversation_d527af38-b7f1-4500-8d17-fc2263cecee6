﻿#pragma checksum "..\..\..\Views\AddSupplierCustomerWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "6A909464AF44F5D403B63E7C9779858E0D191E2B17D944B2789DA1451F42B740"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem.Views {
    
    
    /// <summary>
    /// AddSupplierCustomerWindow
    /// </summary>
    public partial class AddSupplierCustomerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border HeaderBorder;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderIcon;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HeaderTitle;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock NameLabel;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NameTextBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PhoneTextBox;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MobileTextBox;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox EmailTextBox;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CityComboBox;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PostalCodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox OpeningBalanceTextBox;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CreditLimitTextBox;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PaymentTermsTextBox;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TaxNumberTextBox;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CommercialRegisterTextBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SendNotificationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AllowCreditCheckBox;
        
        #line default
        #line hidden
        
        
        #line 212 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RequireApprovalCheckBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem;component/views/addsuppliercustomerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.HeaderBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 2:
            this.HeaderIcon = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.HeaderTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.CodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.NameLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.NameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.PhoneTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.MobileTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.EmailTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.CityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.PostalCodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.AddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.OpeningBalanceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.CreditLimitTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.PaymentTermsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.TaxNumberTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.CommercialRegisterTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 19:
            this.SendNotificationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 20:
            this.AllowCreditCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.RequireApprovalCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            
            #line 228 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 232 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 236 "..\..\..\Views\AddSupplierCustomerWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

