﻿#pragma checksum "..\..\..\Views\AddItemWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "90FE2DDD66B42DE401CB1023A7052C85F2A0619FC132770524AC1CDED657CBAF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem.Views {
    
    
    /// <summary>
    /// AddItemWindow
    /// </summary>
    public partial class AddItemWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 45 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ItemNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CategoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox BarcodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PurchasePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SalePriceTextBox;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProfitMarginTextBox;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox UnitComboBox;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InitialQuantityTextBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox MinimumStockTextBox;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HasExpiryCheckBox;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExpiryDateLabel;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ExpiryDateTextBox;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ShelfLifeLabel;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ShelfLifeTextBox;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WarningDaysLabel;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WarningDaysTextBox;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox DescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox IsActiveCheckBox;
        
        #line default
        #line hidden
        
        
        #line 230 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox TrackInventoryCheckBox;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AllowNegativeStockCheckBox;
        
        #line default
        #line hidden
        
        
        #line 243 "..\..\..\Views\AddItemWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PrintBarcodeCheckBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem;component/views/additemwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\AddItemWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ItemNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.CategoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.BarcodeTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 80 "..\..\..\Views\AddItemWindow.xaml"
            this.BarcodeTextBox.KeyDown += new System.Windows.Input.KeyEventHandler(this.BarcodeTextBox_KeyDown);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 86 "..\..\..\Views\AddItemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateBarcodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 91 "..\..\..\Views\AddItemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ScanBarcodeButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.PurchasePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.SalePriceTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.ProfitMarginTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.UnitComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.InitialQuantityTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.MinimumStockTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.HasExpiryCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 182 "..\..\..\Views\AddItemWindow.xaml"
            this.HasExpiryCheckBox.Checked += new System.Windows.RoutedEventHandler(this.HasExpiryCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 183 "..\..\..\Views\AddItemWindow.xaml"
            this.HasExpiryCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.HasExpiryCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ExpiryDateLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ExpiryDateTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ShelfLifeLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.ShelfLifeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.WarningDaysLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.WarningDaysTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.DescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.IsActiveCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.TrackInventoryCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.AllowNegativeStockCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 23:
            this.PrintBarcodeCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            
            #line 259 "..\..\..\Views\AddItemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 263 "..\..\..\Views\AddItemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveAndAddButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 267 "..\..\..\Views\AddItemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            
            #line 271 "..\..\..\Views\AddItemWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

