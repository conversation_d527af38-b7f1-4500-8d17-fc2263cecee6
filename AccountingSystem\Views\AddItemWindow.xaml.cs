using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace AccountingSystem.Views
{
    public partial class AddItemWindow : Window
    {
        public AddItemWindow()
        {
            InitializeComponent();
            GenerateBarcode();
        }

        private void GenerateBarcode()
        {
            var random = new Random();
            var barcode = "";
            for (int i = 0; i < 13; i++)
            {
                barcode += random.Next(0, 10).ToString();
            }
            BarcodeTextBox.Text = barcode;
        }

        private void BarcodeTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                // Simulate barcode scan completion
                var barcode = BarcodeTextBox.Text.Trim();
                if (barcode.Length >= 10)
                {
                    MessageBox.Show($"تم مسح الباركود: {barcode}", "مسح الباركود", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }

        private void GenerateBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            GenerateBarcode();
            MessageBox.Show("تم توليد باركود جديد تلقائياً", "توليد باركود", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ScanBarcodeButton_Click(object sender, RoutedEventArgs e)
        {
            BarcodeTextBox.Focus();
            MessageBox.Show("امسح الباركود أو اكتبه في الحقل واضغط Enter", "مسح الباركود", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CalculateProfit()
        {
            if (decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) && 
                decimal.TryParse(SalePriceTextBox.Text, out decimal salePrice) &&
                purchasePrice > 0)
            {
                var profit = salePrice - purchasePrice;
                var profitMargin = (profit / purchasePrice) * 100;
                ProfitMarginTextBox.Text = $"{profitMargin:F2}%";
            }
            else
            {
                ProfitMarginTextBox.Text = "0.00%";
            }
        }

        private void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                var message = $"تم حفظ الصنف بنجاح!\n\n" +
                             $"اسم الصنف: {ItemNameTextBox.Text}\n" +
                             $"الباركود: {BarcodeTextBox.Text}\n" +
                             $"الفئة: {((ComboBoxItem)CategoryComboBox.SelectedItem)?.Content}\n" +
                             $"سعر الشراء: {PurchasePriceTextBox.Text} ر.س\n" +
                             $"سعر البيع: {SalePriceTextBox.Text} ر.س\n" +
                             $"الكمية الابتدائية: {InitialQuantityTextBox.Text}\n" +
                             $"الوحدة: {((ComboBoxItem)UnitComboBox.SelectedItem)?.Content}";

                MessageBox.Show(message, "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                
                if (PrintBarcodeCheckBox.IsChecked == true)
                {
                    MessageBox.Show($"سيتم طباعة باركود للصنف: {ItemNameTextBox.Text}\nالباركود: {BarcodeTextBox.Text}", "طباعة باركود", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                
                this.DialogResult = true;
                this.Close();
            }
        }

        private void SaveAndAddButton_Click(object sender, RoutedEventArgs e)
        {
            if (ValidateInput())
            {
                MessageBox.Show("تم حفظ الصنف بنجاح! يمكنك الآن إضافة صنف آخر.", "تم الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                ClearAllFields();
                ItemNameTextBox.Focus();
            }
        }

        private void ClearButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد مسح جميع الحقول؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                ClearAllFields();
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إلغاء العملية؟ سيتم فقدان البيانات المدخلة.", "تأكيد الإلغاء", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result == MessageBoxResult.Yes)
            {
                this.DialogResult = false;
                this.Close();
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(ItemNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الصنف", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                ItemNameTextBox.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(BarcodeTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الباركود", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                BarcodeTextBox.Focus();
                return false;
            }

            if (CategoryComboBox.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الفئة", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                CategoryComboBox.Focus();
                return false;
            }

            if (!decimal.TryParse(PurchasePriceTextBox.Text, out decimal purchasePrice) || purchasePrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر شراء صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                PurchasePriceTextBox.Focus();
                return false;
            }

            if (!decimal.TryParse(SalePriceTextBox.Text, out decimal salePrice) || salePrice <= 0)
            {
                MessageBox.Show("يرجى إدخال سعر بيع صحيح", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                SalePriceTextBox.Focus();
                return false;
            }

            if (salePrice <= purchasePrice)
            {
                var result = MessageBox.Show("سعر البيع أقل من أو يساوي سعر الشراء. هل تريد المتابعة؟", "تحذير", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                if (result == MessageBoxResult.No)
                {
                    SalePriceTextBox.Focus();
                    return false;
                }
            }

            if (!int.TryParse(MinimumStockTextBox.Text, out int minStock) || minStock < 0)
            {
                MessageBox.Show("يرجى إدخال حد أدنى صحيح للمخزون", "خطأ في البيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                MinimumStockTextBox.Focus();
                return false;
            }

            return true;
        }

        private void ClearAllFields()
        {
            ItemNameTextBox.Clear();
            GenerateBarcode();
            CategoryComboBox.SelectedIndex = -1;
            PurchasePriceTextBox.Clear();
            SalePriceTextBox.Clear();
            ProfitMarginTextBox.Text = "0.00%";
            UnitComboBox.SelectedIndex = 0;
            InitialQuantityTextBox.Text = "0";
            MinimumStockTextBox.Text = "5";
            DescriptionTextBox.Clear();
            IsActiveCheckBox.IsChecked = true;
            TrackInventoryCheckBox.IsChecked = true;
            AllowNegativeStockCheckBox.IsChecked = false;
            PrintBarcodeCheckBox.IsChecked = false;
            
            ItemNameTextBox.Focus();
        }
    }
}
