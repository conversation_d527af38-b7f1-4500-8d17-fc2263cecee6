<Window x:Class="AccountingSystem.Views.AddAccountWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة حساب جديد - نظام المحاسبة المتقدم" 
        Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#4CAF50">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="➕" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock Text="إضافة حساب جديد" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="30,20">
                
                <!-- Account Number -->
                <TextBlock Text="رقم الحساب *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="AccountNumberTextBox" 
                         Height="35" FontSize="14" Padding="10"
                         Background="#F9F9F9" Margin="0,0,0,15"/>
                
                <!-- Account Name -->
                <TextBlock Text="اسم الحساب *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="AccountNameTextBox" 
                         Height="35" FontSize="14" Padding="10"
                         Background="#F9F9F9" Margin="0,0,0,15"/>
                
                <!-- Account Type -->
                <TextBlock Text="نوع الحساب *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="AccountTypeComboBox"
                          Height="35" FontSize="14" Padding="10"
                          Background="#F9F9F9" Margin="0,0,0,15">
                    <ComboBoxItem Content="الأصول"/>
                    <ComboBoxItem Content="الخصوم"/>
                    <ComboBoxItem Content="حقوق الملكية"/>
                    <ComboBoxItem Content="الإيرادات"/>
                    <ComboBoxItem Content="المصروفات"/>
                </ComboBox>
                
                <!-- Parent Account -->
                <TextBlock Text="الحساب الرئيسي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="ParentAccountComboBox"
                          Height="35" FontSize="14" Padding="10"
                          Background="#F9F9F9" Margin="0,0,0,15">
                    <ComboBoxItem Content="لا يوجد" IsSelected="True"/>
                    <ComboBoxItem Content="الأصول الثابتة"/>
                    <ComboBoxItem Content="الأصول المتداولة"/>
                    <ComboBoxItem Content="الخصوم المتداولة"/>
                    <ComboBoxItem Content="الخصوم طويلة الأجل"/>
                </ComboBox>
                
                <!-- Initial Balance -->
                <TextBlock Text="الرصيد الافتتاحي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="InitialBalanceTextBox" 
                         Height="35" FontSize="14" Padding="10"
                         Background="#F9F9F9" Margin="0,0,0,15"
                         Text="0.00"/>
                
                <!-- Currency -->
                <TextBlock Text="العملة" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CurrencyComboBox"
                          Height="35" FontSize="14" Padding="10"
                          Background="#F9F9F9" Margin="0,0,0,15">
                    <ComboBoxItem Content="ريال سعودي (ر.س)" IsSelected="True"/>
                    <ComboBoxItem Content="دولار أمريكي ($)"/>
                    <ComboBoxItem Content="يورو (€)"/>
                    <ComboBoxItem Content="جنيه إسترليني (£)"/>
                </ComboBox>
                
                <!-- Description -->
                <TextBlock Text="الوصف" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="DescriptionTextBox" 
                         Height="80" FontSize="14" Padding="10"
                         Background="#F9F9F9" Margin="0,0,0,15"
                         TextWrapping="Wrap" AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"/>
                
                <!-- Active Status -->
                <CheckBox x:Name="IsActiveCheckBox" 
                          Content="الحساب نشط" 
                          FontSize="14" 
                          IsChecked="True"
                          Margin="0,10,0,0"/>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ" 
                        Width="120" Height="40" FontSize="14" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White" Margin="10,0"
                        Click="SaveButton_Click"/>
                <Button Content="🔄 مسح الحقول" 
                        Width="120" Height="40" FontSize="14"
                        Background="#FF9800" Foreground="White" Margin="10,0"
                        Click="ClearButton_Click"/>
                <Button Content="❌ إلغاء" 
                        Width="120" Height="40" FontSize="14"
                        Background="#F44336" Foreground="White" Margin="10,0"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
