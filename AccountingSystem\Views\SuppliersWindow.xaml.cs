using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class SuppliersWindow : Window
    {
        private List<SupplierCustomer> suppliersCustomers;

        public SuppliersWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            suppliersCustomers = new List<SupplierCustomer>
            {
                // Suppliers
                new SupplierCustomer { Code = "SUP001", Name = "مؤسسة النور للتوريدات", Type = "مورد", Phone = "**********", Email = "<EMAIL>", City = "الرياض", Balance = "-15,000.00", Status = "نشط" },
                new SupplierCustomer { Code = "SUP002", Name = "مصنع الجودة للمواد الغذائية", Type = "مورد", Phone = "**********", Email = "<EMAIL>", City = "جدة", Balance = "-8,500.00", Status = "نشط" },
                new SupplierCustomer { Code = "SUP003", Name = "شركة التقنية المتطورة", Type = "مورد", Phone = "0551122334", Email = "<EMAIL>", City = "الدمام", Balance = "-22,000.00", Status = "نشط" },
                new SupplierCustomer { Code = "SUP004", Name = "مؤسسة الإمداد التجارية", Type = "مورد", Phone = "0544455566", Email = "<EMAIL>", City = "مكة", Balance = "-5,200.00", Status = "نشط" },
                new SupplierCustomer { Code = "SUP005", Name = "مصنع الأثاث الحديث", Type = "مورد", Phone = "0533344455", Email = "<EMAIL>", City = "الطائف", Balance = "0.00", Status = "غير نشط" },
                
                // Customers
                new SupplierCustomer { Code = "CUS001", Name = "شركة الأمل التجارية", Type = "عميل", Phone = "0501111222", Email = "<EMAIL>", City = "الرياض", Balance = "12,500.00", Status = "نشط" },
                new SupplierCustomer { Code = "CUS002", Name = "متجر الفجر للإلكترونيات", Type = "عميل", Phone = "0502223333", Email = "<EMAIL>", City = "جدة", Balance = "8,750.00", Status = "نشط" },
                new SupplierCustomer { Code = "CUS003", Name = "شركة البناء الحديث", Type = "عميل", Phone = "0503334444", Email = "<EMAIL>", City = "الدمام", Balance = "25,000.00", Status = "نشط" },
                new SupplierCustomer { Code = "CUS004", Name = "مكتبة المعرفة", Type = "عميل", Phone = "0504445555", Email = "<EMAIL>", City = "المدينة", Balance = "3,200.00", Status = "نشط" },
                new SupplierCustomer { Code = "CUS005", Name = "مطعم الذواقة", Type = "عميل", Phone = "0505556666", Email = "<EMAIL>", City = "الخبر", Balance = "6,800.00", Status = "نشط" },
                new SupplierCustomer { Code = "CUS006", Name = "صيدلية الشفاء", Type = "عميل", Phone = "0506667777", Email = "<EMAIL>", City = "أبها", Balance = "-1,500.00", Status = "محظور" }
            };

            SuppliersDataGrid.ItemsSource = suppliersCustomers;
            UpdateStatusAndTotals();
        }

        private void UpdateStatusAndTotals()
        {
            StatusLabel.Text = $"جاهز - {suppliersCustomers.Count} مورد/عميل";
            
            var suppliers = suppliersCustomers.Where(s => s.Type == "مورد").ToList();
            var customers = suppliersCustomers.Where(s => s.Type == "عميل").ToList();
            
            decimal totalSuppliersBalance = 0;
            decimal totalCustomersBalance = 0;
            
            foreach (var supplier in suppliers)
            {
                if (decimal.TryParse(supplier.Balance.Replace(",", "").Replace("-", ""), out decimal balance))
                {
                    totalSuppliersBalance += supplier.Balance.Contains("-") ? balance : -balance;
                }
            }
            
            foreach (var customer in customers)
            {
                if (decimal.TryParse(customer.Balance.Replace(",", "").Replace("-", ""), out decimal balance))
                {
                    totalCustomersBalance += customer.Balance.Contains("-") ? -balance : balance;
                }
            }
            
            TotalSuppliersBalanceLabel.Text = $"{Math.Abs(totalSuppliersBalance):N2} ر.س";
            TotalCustomersBalanceLabel.Text = $"{totalCustomersBalance:N2} ر.س";
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox.Text == "البحث في الموردين والعملاء...")
            {
                textBox.Text = "";
                textBox.Foreground = Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = "البحث في الموردين والعملاء...";
                textBox.Foreground = Brushes.Gray;
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطبيق البحث قريباً", "البحث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var addSupplierWindow = new AddSupplierCustomerWindow("مورد");
            if (addSupplierWindow.ShowDialog() == true)
            {
                LoadSampleData();
            }
        }

        private void AddCustomerButton_Click(object sender, RoutedEventArgs e)
        {
            var addCustomerWindow = new AddSupplierCustomerWindow("عميل");
            if (addCustomerWindow.ShowDialog() == true)
            {
                LoadSampleData();
            }
        }

        private void ViewSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as SupplierCustomer;
            if (item != null)
            {
                MessageBox.Show($"تفاصيل {item.Type}:\n\nالكود: {item.Code}\nالاسم: {item.Name}\nالهاتف: {item.Phone}\nالبريد الإلكتروني: {item.Email}\nالمدينة: {item.City}\nالرصيد: {item.Balance} ر.س\nالحالة: {item.Status}", 
                               $"تفاصيل {item.Type}", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void EditSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as SupplierCustomer;
            if (item != null)
            {
                MessageBox.Show($"تعديل {item.Type}: {item.Name}", $"تعديل {item.Type}", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteSupplierButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as SupplierCustomer;
            if (item != null)
            {
                var result = MessageBox.Show($"هل تريد حذف {item.Type}: {item.Name}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                if (result == MessageBoxResult.Yes)
                {
                    suppliersCustomers.Remove(item);
                    SuppliersDataGrid.Items.Refresh();
                    UpdateStatusAndTotals();
                }
            }
        }

        private void SupplierReportButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as SupplierCustomer;
            if (item != null)
            {
                MessageBox.Show($"تقرير {item.Type}: {item.Name}", $"تقرير {item.Type}", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void PaymentButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var item = button?.DataContext as SupplierCustomer;
            if (item != null)
            {
                MessageBox.Show($"تسجيل دفعة لـ {item.Type}: {item.Name}\nالرصيد الحالي: {item.Balance} ر.س", "تسجيل دفعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير البيانات قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم استيراد البيانات قريباً", "استيراد", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class SupplierCustomer
    {
        public string Code { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public string Phone { get; set; }
        public string Email { get; set; }
        public string City { get; set; }
        public string Balance { get; set; }
        public string Status { get; set; }
    }
}
