# Test Login Script
Write-Host "=== Login Test ===" -ForegroundColor Green

# Check if the application is running
$process = Get-Process AccountingSystem -ErrorAction SilentlyContinue
if ($process) {
    Write-Host "Program is running normally" -ForegroundColor Green
    Write-Host "Process ID: $($process.Id)" -ForegroundColor Yellow
    Write-Host "Memory Usage: $([math]::Round($process.WorkingSet/1MB, 2)) MB" -ForegroundColor Yellow
} else {
    Write-Host "Program is not running" -ForegroundColor Red
    Write-Host "Starting program..." -ForegroundColor Yellow
    Start-Process ".\AccountingSystem.exe"
    Start-Sleep 3
}

Write-Host ""
Write-Host "=== Login Information ===" -ForegroundColor Cyan
Write-Host "Username: admin" -ForegroundColor White
Write-Host "Password: admin123" -ForegroundColor White
Write-Host ""
Write-Host "=== Login Steps ===" -ForegroundColor Cyan
Write-Host "1. Make sure username field contains: admin" -ForegroundColor White
Write-Host "2. Make sure password field contains: admin123" -ForegroundColor White
Write-Host "3. Click the blue 'Login' button" -ForegroundColor White
Write-Host "4. Or press Enter in any field" -ForegroundColor White
Write-Host ""
Write-Host "=== If button is not visible ===" -ForegroundColor Yellow
Write-Host "- Try resizing the window" -ForegroundColor White
Write-Host "- Try scrolling down" -ForegroundColor White
Write-Host "- Press Tab twice then Enter" -ForegroundColor White
Write-Host ""
Write-Host "Program is ready to use!" -ForegroundColor Green
