﻿#pragma checksum "..\..\..\Views\SimpleMainWindow.xaml" "{8829d00f-11b8-4213-878b-770e8597ac16}" "04BB50897E3E83ED6AA900BBBF5C73553608E8D53127EE59CFC9F9020AC926C1"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace AccountingSystem.Views {
    
    
    /// <summary>
    /// SimpleMainWindow
    /// </summary>
    public partial class SimpleMainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DashboardButton;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MainContentArea;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DashboardContent;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid AccountsContent;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid JournalEntriesContent;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid InvoicesContent;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\Views\SimpleMainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ReportsContent;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/AccountingSystem;component/views/simplemainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\Views\SimpleMainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "4.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 37 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.LogoutButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DashboardButton = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\Views\SimpleMainWindow.xaml"
            this.DashboardButton.Click += new System.Windows.RoutedEventHandler(this.DashboardButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 71 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AccountsButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 78 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddAccountButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 88 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddJournalEntryButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 95 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.JournalEntriesButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 105 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddSalesInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 112 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddPurchaseInvoiceButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 119 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.InvoicesButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 129 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ReportsButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 136 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.IncomeStatementButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 143 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BalanceSheetButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 153 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SettingsButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 160 "..\..\..\Views\SimpleMainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.UserManagementButton_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.MainContentArea = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            this.DashboardContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 17:
            this.AccountsContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 18:
            this.JournalEntriesContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 19:
            this.InvoicesContent = ((System.Windows.Controls.Grid)(target));
            return;
            case 20:
            this.ReportsContent = ((System.Windows.Controls.Grid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

