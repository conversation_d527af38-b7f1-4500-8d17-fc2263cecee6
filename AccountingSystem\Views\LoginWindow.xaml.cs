using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace AccountingSystem.Views
{
    public partial class LoginWindow : Window
    {
        public LoginWindow()
        {
            InitializeComponent();

            // Set default password for testing
            PasswordBox.Password = "admin123";

            // Focus on username
            Loaded += (s, e) => UsernameTextBox.Focus();

            // Handle Enter key
            UsernameTextBox.KeyDown += (s, e) => {
                if (e.Key == Key.Enter) PasswordBox.Focus();
            };

            PasswordBox.KeyDown += (s, e) => {
                if (e.Key == Key.Enter) LoginButton_Click(null, null);
            };
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Application.Current.Shutdown();
        }

        private void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Hide previous messages
                ErrorMessage.Visibility = Visibility.Collapsed;
                SuccessMessage.Visibility = Visibility.Collapsed;

                string username = UsernameTextBox.Text?.Trim() ?? "";
                string password = PasswordBox.Password ?? "";

                // Validate
                if (string.IsNullOrEmpty(username))
                {
                    ShowError("يرجى إدخال اسم المستخدم");
                    return;
                }

                if (string.IsNullOrEmpty(password))
                {
                    ShowError("يرجى إدخال كلمة المرور");
                    return;
                }

                // Check credentials
                if (username == "admin" && password == "admin123")
                {
                    ShowSuccess("تم تسجيل الدخول بنجاح!");

                    // Wait a moment then open main window
                    var timer = new System.Windows.Threading.DispatcherTimer();
                    timer.Interval = TimeSpan.FromSeconds(1);
                    timer.Tick += (s, args) => {
                        timer.Stop();
                        try
                        {
                            // Create a simple main window instead
                            var mainWindow = new Window
                            {
                                Title = "نظام المحاسبة المتقدم - النافذة الرئيسية",
                                Width = 800,
                                Height = 600,
                                WindowStartupLocation = WindowStartupLocation.CenterScreen,
                                Background = System.Windows.Media.Brushes.White,
                                Content = new TextBlock
                                {
                                    Text = "مرحباً بك في نظام المحاسبة المتقدم!\n\nتم تسجيل الدخول بنجاح.",
                                    FontSize = 24,
                                    HorizontalAlignment = HorizontalAlignment.Center,
                                    VerticalAlignment = VerticalAlignment.Center,
                                    TextAlignment = TextAlignment.Center
                                }
                            };
                            mainWindow.Show();
                            this.Close();
                        }
                        catch (Exception ex)
                        {
                            ShowError($"خطأ في فتح النافذة الرئيسية: {ex.Message}");
                        }
                    };
                    timer.Start();
                }
                else
                {
                    ShowError("اسم المستخدم أو كلمة المرور غير صحيحة");
                }
            }
            catch (Exception ex)
            {
                ShowError($"خطأ: {ex.Message}");
            }
        }

        private void ShowError(string message)
        {
            ErrorMessage.Text = message;
            ErrorMessage.Visibility = Visibility.Visible;
            SuccessMessage.Visibility = Visibility.Collapsed;
        }

        private void ShowSuccess(string message)
        {
            SuccessMessage.Text = message;
            SuccessMessage.Visibility = Visibility.Visible;
            ErrorMessage.Visibility = Visibility.Collapsed;
        }
    }
}
