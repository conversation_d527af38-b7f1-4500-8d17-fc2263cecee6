<Window x:Class="AccountingSystem.Views.AddSupplierCustomerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إضافة مورد/عميل جديد - نظام المحاسبة المتقدم" 
        Height="600" Width="700"
        WindowStartupLocation="CenterScreen"
        Background="White"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" x:Name="HeaderBorder" Background="#607D8B">
            <Grid>
                <StackPanel Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock x:Name="HeaderIcon" Text="🏭" FontSize="24" Margin="0,0,10,0"/>
                    <TextBlock x:Name="HeaderTitle" Text="إضافة مورد جديد" 
                               FontSize="20" FontWeight="Bold"
                               Foreground="White" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="30,20">
                
                <!-- Basic Information -->
                <TextBlock Text="المعلومات الأساسية" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#607D8B"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="الكود *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CodeTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" IsReadOnly="True"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                        <TextBlock x:Name="NameLabel" Text="اسم المورد *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="NameTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"/>
                    </StackPanel>
                </Grid>
                
                <!-- Contact Information -->
                <TextBlock Text="معلومات الاتصال" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#607D8B"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="رقم الهاتف *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PhoneTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                        <TextBlock Text="رقم الجوال" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="MobileTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"/>
                    </StackPanel>
                </Grid>
                
                <TextBlock Text="البريد الإلكتروني" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="EmailTextBox" 
                         Height="35" FontSize="14" Padding="10"
                         Background="#F9F9F9" Margin="0,0,0,15"/>
                
                <!-- Address Information -->
                <TextBlock Text="معلومات العنوان" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#607D8B"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="المدينة *" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CityComboBox"
                                  Height="35" FontSize="14" Padding="10"
                                  Background="#F9F9F9">
                            <ComboBoxItem Content="الرياض"/>
                            <ComboBoxItem Content="جدة"/>
                            <ComboBoxItem Content="الدمام"/>
                            <ComboBoxItem Content="مكة"/>
                            <ComboBoxItem Content="المدينة"/>
                            <ComboBoxItem Content="الطائف"/>
                            <ComboBoxItem Content="الخبر"/>
                            <ComboBoxItem Content="أبها"/>
                            <ComboBoxItem Content="تبوك"/>
                            <ComboBoxItem Content="القصيم"/>
                        </ComboBox>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                        <TextBlock Text="الرمز البريدي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PostalCodeTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"/>
                    </StackPanel>
                </Grid>
                
                <TextBlock Text="العنوان التفصيلي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="AddressTextBox" 
                         Height="60" FontSize="14" Padding="10"
                         Background="#F9F9F9" Margin="0,0,0,20"
                         TextWrapping="Wrap" AcceptsReturn="True"/>
                
                <!-- Financial Information -->
                <TextBlock Text="المعلومات المالية" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#607D8B"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="الرصيد الافتتاحي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="OpeningBalanceTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Text="0.00"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="5,0">
                        <TextBlock Text="حد الائتمان" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CreditLimitTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Text="0.00"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2" Margin="10,0,0,0">
                        <TextBlock Text="مدة السداد (يوم)" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="PaymentTermsTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9" Text="30"/>
                    </StackPanel>
                </Grid>
                
                <!-- Tax Information -->
                <TextBlock Text="المعلومات الضريبية" FontSize="16" FontWeight="Bold" Margin="0,0,0,15" Foreground="#607D8B"/>
                
                <Grid Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="الرقم الضريبي" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TaxNumberTextBox" 
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="10,0,0,0">
                        <TextBlock Text="السجل التجاري" FontSize="14" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="CommercialRegisterTextBox"
                                 Height="35" FontSize="14" Padding="10"
                                 Background="#F9F9F9"/>
                    </StackPanel>
                </Grid>
                
                <!-- Status -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <CheckBox x:Name="IsActiveCheckBox" 
                                  Content="نشط" 
                                  FontSize="14" 
                                  IsChecked="True"
                                  Margin="0,10,0,0"/>
                        
                        <CheckBox x:Name="SendNotificationsCheckBox" 
                                  Content="إرسال إشعارات" 
                                  FontSize="14" 
                                  IsChecked="True"
                                  Margin="0,5,0,0"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="20,0,0,0">
                        <CheckBox x:Name="AllowCreditCheckBox" 
                                  Content="السماح بالائتمان" 
                                  FontSize="14" 
                                  IsChecked="True"
                                  Margin="0,10,0,0"/>
                        
                        <CheckBox x:Name="RequireApprovalCheckBox" 
                                  Content="يتطلب موافقة" 
                                  FontSize="14" 
                                  Margin="0,5,0,0"/>
                    </StackPanel>
                </Grid>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Footer Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" BorderBrush="#DDD" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                <Button Content="💾 حفظ" 
                        Width="120" Height="40" FontSize="14" FontWeight="Bold"
                        Background="#4CAF50" Foreground="White" Margin="10,0"
                        Click="SaveButton_Click"/>
                <Button Content="🔄 مسح الحقول" 
                        Width="120" Height="40" FontSize="14"
                        Background="#FF9800" Foreground="White" Margin="10,0"
                        Click="ClearButton_Click"/>
                <Button Content="❌ إلغاء" 
                        Width="120" Height="40" FontSize="14"
                        Background="#F44336" Foreground="White" Margin="10,0"
                        Click="CancelButton_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
