using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class SimpleSettingsWindow : Window
    {
        public SimpleSettingsWindow()
        {
            try
            {
                InitializeComponent();
                LoadCurrentSettings();
                UpdatePreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في تحميل نافذة الإعدادات: " + ex.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadCurrentSettings()
        {
            // Load saved settings or use defaults
            // This would normally load from a settings file or database
        }

        private void UpdatePreview()
        {
            try
            {
                var dateFormat = ((ComboBoxItem)DateFormatComboBox.SelectedItem)?.Content.ToString() ?? "dd/MM/yyyy";
                var timeFormat = ((ComboBoxItem)TimeFormatComboBox.SelectedItem)?.Content.ToString() ?? "24 ساعة (HH:mm)";
                var numberType = ((ComboBoxItem)NumberTypeComboBox.SelectedItem)?.Content.ToString() ?? "أرقام هندية";
                var currencySymbol = CurrencySymbolTextBox.Text;
                
                var sampleDate = DateTime.Now.ToString("dd/MM/yyyy");
                var sampleTime = DateTime.Now.ToString("HH:mm");
                var sampleAmount = numberType.Contains("عربية") ? "١٬٢٣٤٫٥٦" : "1,234.56";
                
                PreviewTextBlock.Text = "التاريخ: " + sampleDate + " - الوقت: " + sampleTime + " - المبلغ: " + sampleAmount + " " + currencySymbol;
            }
            catch
            {
                // Ignore preview errors
            }
        }

        private void ColorButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var color = button.Tag.ToString();
            MessageBox.Show("تم اختيار اللون: " + color, "تغيير اللون", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void SaveSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var companyName = CompanyNameTextBox.Text;
                var language = ((ComboBoxItem)LanguageComboBox.SelectedItem)?.Content.ToString() ?? "العربية";
                var currency = ((ComboBoxItem)BaseCurrencyComboBox.SelectedItem)?.Content.ToString() ?? "ريال سعودي";
                var theme = ((ComboBoxItem)ThemeComboBox.SelectedItem)?.Content.ToString() ?? "فاتح";

                var message = "تم حفظ الإعدادات بنجاح!\n\n" +
                             "اسم الشركة: " + companyName + "\n" +
                             "اللغة: " + language + "\n" +
                             "العملة: " + currency + "\n" +
                             "المظهر: " + theme;

                MessageBox.Show(message, "حفظ الإعدادات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في حفظ الإعدادات: " + ex.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RestoreDefaultsButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد استعادة الإعدادات الافتراضية؟ سيتم فقدان جميع التخصيصات.", "استعادة الافتراضي", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            if (result == MessageBoxResult.Yes)
            {
                LoadDefaultSettings();
                MessageBox.Show("تم استعادة الإعدادات الافتراضية", "استعادة الافتراضي", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void LoadDefaultSettings()
        {
            try
            {
                // Reset to default values
                CompanyNameTextBox.Text = "شركة المحاسبة المتقدمة";
                TaxNumberTextBox.Text = "300123456789003";
                PhoneTextBox.Text = "+966 11 123 4567";
                EmailTextBox.Text = "<EMAIL>";
                
                LanguageComboBox.SelectedIndex = 0;
                NumberTypeComboBox.SelectedIndex = 1;
                DateFormatComboBox.SelectedIndex = 0;
                TimeFormatComboBox.SelectedIndex = 0;
                
                BaseCurrencyComboBox.SelectedIndex = 0;
                CurrencySymbolTextBox.Text = "ر.س";
                CurrencyPositionComboBox.SelectedIndex = 0;
                DecimalPlacesComboBox.SelectedIndex = 2;
                
                ThemeComboBox.SelectedIndex = 0;
                FontSizeComboBox.SelectedIndex = 1;
                
                UpdatePreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في استعادة الإعدادات الافتراضية: " + ex.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
