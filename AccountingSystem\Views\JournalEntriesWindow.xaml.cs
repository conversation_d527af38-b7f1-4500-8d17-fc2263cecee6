using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace AccountingSystem.Views
{
    public partial class JournalEntriesWindow : Window
    {
        private List<JournalEntryItem> journalEntries;

        public JournalEntriesWindow()
        {
            InitializeComponent();
            LoadSampleData();
        }

        private void LoadSampleData()
        {
            journalEntries = new List<JournalEntryItem>
            {
                new JournalEntryItem { EntryNumber = "JE001", Date = "2024-01-15", Description = "قيد افتتاحي - رأس المال", Amount = "200,000.00", Status = "مؤكد" },
                new JournalEntryItem { EntryNumber = "JE002", Date = "2024-01-16", Description = "شراء معدات مكتبية", Amount = "15,000.00", Status = "مؤكد" },
                new JournalEntryItem { EntryNumber = "JE003", Date = "2024-01-17", Description = "مبيعات نقدية", Amount = "25,000.00", Status = "مؤكد" },
                new JournalEntryItem { EntryNumber = "JE004", Date = "2024-01-18", Description = "دفع إيجار المكتب", Amount = "8,000.00", Status = "مؤكد" },
                new JournalEntryItem { EntryNumber = "JE005", Date = "2024-01-19", Description = "شراء بضاعة للمخزن", Amount = "50,000.00", Status = "مؤكد" },
                new JournalEntryItem { EntryNumber = "JE006", Date = "2024-01-20", Description = "مبيعات آجلة", Amount = "35,000.00", Status = "مسودة" },
                new JournalEntryItem { EntryNumber = "JE007", Date = "2024-01-21", Description = "دفع رواتب الموظفين", Amount = "12,000.00", Status = "مؤكد" },
                new JournalEntryItem { EntryNumber = "JE008", Date = "2024-01-22", Description = "تحصيل من العملاء", Amount = "20,000.00", Status = "مؤكد" }
            };

            JournalEntriesDataGrid.ItemsSource = journalEntries;
            UpdateStatusAndTotal();
        }

        private void UpdateStatusAndTotal()
        {
            StatusLabel.Text = $"جاهز - {journalEntries.Count} قيد";
            
            decimal total = 0;
            foreach (var entry in journalEntries)
            {
                if (decimal.TryParse(entry.Amount.Replace(",", ""), out decimal amount))
                {
                    total += amount;
                }
            }
            TotalAmountLabel.Text = $"{total:N2} ر.س";
        }

        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (textBox.Text == "البحث في القيود...")
            {
                textBox.Text = "";
                textBox.Foreground = Brushes.Black;
            }
        }

        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            var textBox = sender as TextBox;
            if (string.IsNullOrWhiteSpace(textBox.Text))
            {
                textBox.Text = "البحث في القيود...";
                textBox.Foreground = Brushes.Gray;
            }
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطبيق البحث قريباً", "البحث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void AddJournalEntryButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم فتح نافذة إضافة قيد جديد قريباً", "قيد جديد", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ViewEntryButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var entry = button?.DataContext as JournalEntryItem;
            if (entry != null)
            {
                MessageBox.Show($"عرض تفاصيل القيد: {entry.EntryNumber}\nالتاريخ: {entry.Date}\nالوصف: {entry.Description}\nالمبلغ: {entry.Amount}\nالحالة: {entry.Status}", 
                               "تفاصيل القيد", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void EditEntryButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var entry = button?.DataContext as JournalEntryItem;
            if (entry != null)
            {
                MessageBox.Show($"تعديل القيد: {entry.EntryNumber}", "تعديل قيد", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void DeleteEntryButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var entry = button?.DataContext as JournalEntryItem;
            if (entry != null)
            {
                var result = MessageBox.Show($"هل تريد حذف القيد: {entry.EntryNumber}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                if (result == MessageBoxResult.Yes)
                {
                    journalEntries.Remove(entry);
                    JournalEntriesDataGrid.Items.Refresh();
                    UpdateStatusAndTotal();
                }
            }
        }

        private void PrintEntryButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var entry = button?.DataContext as JournalEntryItem;
            if (entry != null)
            {
                MessageBox.Show($"طباعة القيد: {entry.EntryNumber}", "طباعة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تصدير البيانات قريباً", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            LoadSampleData();
            MessageBox.Show("تم تحديث البيانات", "تحديث", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }
    }

    public class JournalEntryItem
    {
        public string EntryNumber { get; set; }
        public string Date { get; set; }
        public string Description { get; set; }
        public string Amount { get; set; }
        public string Status { get; set; }
    }
}
