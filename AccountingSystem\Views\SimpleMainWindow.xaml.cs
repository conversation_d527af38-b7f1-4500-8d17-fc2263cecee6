using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingSystem.Views
{
    public partial class SimpleMainWindow : Window
    {
        public SimpleMainWindow()
        {
            InitializeComponent();
            ShowDashboard();
        }

        private void ShowDashboard()
        {
            HideAllContent();
            DashboardContent.Visibility = Visibility.Visible;
            UpdateButtonStyles();
            DashboardButton.Background = System.Windows.Media.Brushes.LightBlue;
        }

        private void HideAllContent()
        {
            DashboardContent.Visibility = Visibility.Collapsed;
            AccountsContent.Visibility = Visibility.Collapsed;
            JournalEntriesContent.Visibility = Visibility.Collapsed;
            InvoicesContent.Visibility = Visibility.Collapsed;
            ReportsContent.Visibility = Visibility.Collapsed;
        }

        private void UpdateButtonStyles()
        {
            // Reset all button styles
            DashboardButton.Background = System.Windows.Media.Brushes.White;
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                var loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }

        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            ShowDashboard();
        }

        private void AccountsButton_Click(object sender, RoutedEventArgs e)
        {
            var accountsWindow = new AccountsWindow();
            accountsWindow.ShowDialog();
        }

        private void AddAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var addAccountWindow = new AddAccountWindow();
            addAccountWindow.ShowDialog();
        }

        private void AddJournalEntryButton_Click(object sender, RoutedEventArgs e)
        {
            var addJournalEntryWindow = new AddJournalEntryWindow();
            addJournalEntryWindow.ShowDialog();
        }

        private void JournalEntriesButton_Click(object sender, RoutedEventArgs e)
        {
            var journalEntriesWindow = new JournalEntriesWindow();
            journalEntriesWindow.ShowDialog();
        }

        private void AddSalesInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddInvoiceWindow("مبيعات");
            addInvoiceWindow.ShowDialog();
        }

        private void AddPurchaseInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddInvoiceWindow("مشتريات");
            addInvoiceWindow.ShowDialog();
        }

        private void InvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            var invoicesWindow = new InvoicesWindow();
            invoicesWindow.ShowDialog();
        }

        private void ReportsButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void IncomeStatementButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void BalanceSheetButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void ItemsButton_Click(object sender, RoutedEventArgs e)
        {
            var itemsWindow = new ItemsWindow();
            itemsWindow.ShowDialog();
        }

        private void SuppliersButton_Click(object sender, RoutedEventArgs e)
        {
            var suppliersWindow = new SuppliersWindow();
            suppliersWindow.ShowDialog();
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // عرض معلومات إعدادات النظام الحالية
                var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
                var sampleDate = DateTime.Now.ToString("d", currentCulture);
                var sampleTime = DateTime.Now.ToString("t", currentCulture);
                var sampleCurrency = (1234.56m).ToString("C", currentCulture);
                var sampleNumber = (1234.56m).ToString("N2", currentCulture);

                var message = "إعدادات التنسيق - نظام المحاسبة\n\n" +
                             "يستخدم النظام إعدادات ويندوز التالية:\n\n" +
                             "🌍 المنطقة: " + currentCulture.DisplayName + "\n" +
                             "📅 التاريخ: " + sampleDate + "\n" +
                             "🕐 الوقت: " + sampleTime + "\n" +
                             "💰 العملة: " + sampleCurrency + "\n" +
                             "🔢 الأرقام: " + sampleNumber + "\n\n" +
                             "لتغيير هذه الإعدادات:\n" +
                             "1. افتح إعدادات ويندوز\n" +
                             "2. اذهب إلى الوقت واللغة\n" +
                             "3. اختر المنطقة والتنسيق المطلوب\n\n" +
                             "هل تريد فتح إعدادات ويندوز؟";

                var result = MessageBox.Show(message, "إعدادات التنسيق", MessageBoxButton.YesNo, MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // فتح إعدادات ويندوز للمنطقة والتنسيق
                        System.Diagnostics.Process.Start("ms-settings:regionlanguage");
                    }
                    catch
                    {
                        // إذا فشل فتح الإعدادات الحديثة، جرب الطريقة التقليدية
                        try
                        {
                            System.Diagnostics.Process.Start("intl.cpl");
                        }
                        catch
                        {
                            MessageBox.Show("لا يمكن فتح إعدادات ويندوز تلقائياً.\nيرجى فتحها يدوياً من قائمة ابدأ.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في عرض إعدادات النظام: " + ex.Message, "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UserManagementButton_Click(object sender, RoutedEventArgs e)
        {
            ShowMessage("إدارة المستخدمين", "هنا سيتم إدارة المستخدمين");
        }

        private void ShowMessage(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
