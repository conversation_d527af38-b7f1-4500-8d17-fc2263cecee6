using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingSystem.Views
{
    public partial class SimpleMainWindow : Window
    {
        public SimpleMainWindow()
        {
            InitializeComponent();
            ShowDashboard();
        }

        private void ShowDashboard()
        {
            HideAllContent();
            DashboardContent.Visibility = Visibility.Visible;
            UpdateButtonStyles();
            DashboardButton.Background = System.Windows.Media.Brushes.LightBlue;
        }

        private void HideAllContent()
        {
            DashboardContent.Visibility = Visibility.Collapsed;
            AccountsContent.Visibility = Visibility.Collapsed;
            JournalEntriesContent.Visibility = Visibility.Collapsed;
            InvoicesContent.Visibility = Visibility.Collapsed;
            ReportsContent.Visibility = Visibility.Collapsed;
        }

        private void UpdateButtonStyles()
        {
            // Reset all button styles
            DashboardButton.Background = System.Windows.Media.Brushes.White;
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                var loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }

        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            ShowDashboard();
        }

        private void AccountsButton_Click(object sender, RoutedEventArgs e)
        {
            var accountsWindow = new AccountsWindow();
            accountsWindow.ShowDialog();
        }

        private void AddAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var addAccountWindow = new AddAccountWindow();
            addAccountWindow.ShowDialog();
        }

        private void AddJournalEntryButton_Click(object sender, RoutedEventArgs e)
        {
            var addJournalEntryWindow = new AddJournalEntryWindow();
            addJournalEntryWindow.ShowDialog();
        }

        private void JournalEntriesButton_Click(object sender, RoutedEventArgs e)
        {
            var journalEntriesWindow = new JournalEntriesWindow();
            journalEntriesWindow.ShowDialog();
        }

        private void AddSalesInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddInvoiceWindow("مبيعات");
            addInvoiceWindow.ShowDialog();
        }

        private void AddPurchaseInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddInvoiceWindow("مشتريات");
            addInvoiceWindow.ShowDialog();
        }

        private void InvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            var invoicesWindow = new InvoicesWindow();
            invoicesWindow.ShowDialog();
        }

        private void ReportsButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void IncomeStatementButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void BalanceSheetButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void ItemsButton_Click(object sender, RoutedEventArgs e)
        {
            var itemsWindow = new ItemsWindow();
            itemsWindow.ShowDialog();
        }

        private void SuppliersButton_Click(object sender, RoutedEventArgs e)
        {
            var suppliersWindow = new SuppliersWindow();
            suppliersWindow.ShowDialog();
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            var settingsWindow = new SettingsWindow();
            settingsWindow.ShowDialog();
        }

        private void UserManagementButton_Click(object sender, RoutedEventArgs e)
        {
            ShowMessage("إدارة المستخدمين", "هنا سيتم إدارة المستخدمين");
        }

        private void ShowMessage(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
