using System;
using System.Windows;
using System.Windows.Controls;

namespace AccountingSystem.Views
{
    public partial class SimpleMainWindow : Window
    {
        public SimpleMainWindow()
        {
            InitializeComponent();
            ShowDashboard();
        }

        private void ShowDashboard()
        {
            HideAllContent();
            DashboardContent.Visibility = Visibility.Visible;
            UpdateButtonStyles();
            DashboardButton.Background = System.Windows.Media.Brushes.LightBlue;
        }

        private void HideAllContent()
        {
            DashboardContent.Visibility = Visibility.Collapsed;
            AccountsContent.Visibility = Visibility.Collapsed;
            JournalEntriesContent.Visibility = Visibility.Collapsed;
            InvoicesContent.Visibility = Visibility.Collapsed;
            ReportsContent.Visibility = Visibility.Collapsed;
        }

        private void UpdateButtonStyles()
        {
            // Reset all button styles
            DashboardButton.Background = System.Windows.Media.Brushes.White;
        }

        private void LogoutButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show("هل تريد تسجيل الخروج؟", "تأكيد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            if (result == MessageBoxResult.Yes)
            {
                var loginWindow = new LoginWindow();
                loginWindow.Show();
                this.Close();
            }
        }

        private void DashboardButton_Click(object sender, RoutedEventArgs e)
        {
            ShowDashboard();
        }

        private void AccountsButton_Click(object sender, RoutedEventArgs e)
        {
            var accountsWindow = new AccountsWindow();
            accountsWindow.ShowDialog();
        }

        private void AddAccountButton_Click(object sender, RoutedEventArgs e)
        {
            var addAccountWindow = new AddAccountWindow();
            addAccountWindow.ShowDialog();
        }

        private void AddJournalEntryButton_Click(object sender, RoutedEventArgs e)
        {
            var addJournalEntryWindow = new AddJournalEntryWindow();
            addJournalEntryWindow.ShowDialog();
        }

        private void JournalEntriesButton_Click(object sender, RoutedEventArgs e)
        {
            var journalEntriesWindow = new JournalEntriesWindow();
            journalEntriesWindow.ShowDialog();
        }

        private void AddSalesInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddInvoiceWindow("مبيعات");
            addInvoiceWindow.ShowDialog();
        }

        private void AddPurchaseInvoiceButton_Click(object sender, RoutedEventArgs e)
        {
            var addInvoiceWindow = new AddInvoiceWindow("مشتريات");
            addInvoiceWindow.ShowDialog();
        }

        private void InvoicesButton_Click(object sender, RoutedEventArgs e)
        {
            var invoicesWindow = new InvoicesWindow();
            invoicesWindow.ShowDialog();
        }

        private void ReportsButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void IncomeStatementButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void BalanceSheetButton_Click(object sender, RoutedEventArgs e)
        {
            var reportsWindow = new ReportsWindow();
            reportsWindow.ShowDialog();
        }

        private void ItemsButton_Click(object sender, RoutedEventArgs e)
        {
            var itemsWindow = new ItemsWindow();
            itemsWindow.ShowDialog();
        }

        private void SuppliersButton_Click(object sender, RoutedEventArgs e)
        {
            var suppliersWindow = new SuppliersWindow();
            suppliersWindow.ShowDialog();
        }

        private void SettingsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // عرض إعدادات النظام البسيطة
                var currentCulture = System.Globalization.CultureInfo.CurrentCulture;
                var sampleDate = DateTime.Now.ToString("d", currentCulture);
                var sampleTime = DateTime.Now.ToString("t", currentCulture);
                var sampleCurrency = (1234.56m).ToString("C", currentCulture);
                var sampleNumber = (1234.56m).ToString("N2", currentCulture);

                var settingsInfo = "⚙️ إعدادات النظام - نظام المحاسبة\n\n" +
                                 "📊 إعدادات التنسيق الحالية (من نظام ويندوز):\n\n" +
                                 "🌍 المنطقة: " + currentCulture.DisplayName + "\n" +
                                 "📅 التاريخ: " + sampleDate + "\n" +
                                 "🕐 الوقت: " + sampleTime + "\n" +
                                 "💰 العملة: " + sampleCurrency + "\n" +
                                 "🔢 الأرقام: " + sampleNumber + "\n" +
                                 "📊 النسبة: " + (15.5m).ToString("N2", currentCulture) + "%\n\n" +
                                 "🏢 معلومات الشركة:\n" +
                                 "• الاسم: شركة المحاسبة المتقدمة\n" +
                                 "• الرقم الضريبي: 300123456789003\n" +
                                 "• الهاتف: +966 11 123 4567\n\n" +
                                 "🔧 لتغيير إعدادات التنسيق:\n" +
                                 "1. افتح إعدادات ويندوز\n" +
                                 "2. اذهب إلى الوقت واللغة\n" +
                                 "3. اختر المنطقة والتنسيق المطلوب\n" +
                                 "4. أعد تشغيل البرنامج\n\n" +
                                 "هل تريد فتح إعدادات ويندوز الآن؟";

                var result = MessageBox.Show(settingsInfo + "\n\n" +
                    "🔘 نعم = فتح إعدادات ويندوز\n" +
                    "🔘 لا = عرض معلومات إضافية\n" +
                    "🔘 إلغاء = إغلاق",
                    "إعدادات النظام", MessageBoxButton.YesNoCancel, MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // محاولة فتح إعدادات ويندوز الحديثة
                        System.Diagnostics.Process.Start("ms-settings:regionlanguage");
                    }
                    catch
                    {
                        try
                        {
                            // إذا فشل، جرب لوحة التحكم التقليدية
                            System.Diagnostics.Process.Start("intl.cpl");
                        }
                        catch
                        {
                            MessageBox.Show("لا يمكن فتح إعدادات ويندوز تلقائياً.\n\n" +
                                          "يرجى فتح إعدادات ويندوز يدوياً:\n" +
                                          "• اضغط على زر ابدأ\n" +
                                          "• اختر الإعدادات (Settings)\n" +
                                          "• اذهب إلى الوقت واللغة\n" +
                                          "• اختر المنطقة",
                                          "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
                else if (result == MessageBoxResult.No)
                {
                    // عرض خيارات إضافية
                    var additionalOptions = "🔧 خيارات إضافية:\n\n" +
                                          "✅ النسخ الاحتياطي التلقائي: مفعل\n" +
                                          "✅ الإشعارات: مفعلة\n" +
                                          "✅ حفظ سجل العمليات: مفعل\n" +
                                          "✅ التحديث التلقائي: مفعل\n\n" +
                                          "📁 مجلد البيانات: " + System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) + "\n\n" +
                                          "ℹ️ جميع الإعدادات تعمل بشكل تلقائي ولا تحتاج تدخل يدوي.";

                    MessageBox.Show(additionalOptions, "معلومات إضافية",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في عرض إعدادات النظام: " + ex.Message, "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UserManagementButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // عرض قائمة المستخدمين الحالية
                var usersInfo = "👥 إدارة المستخدمين - نظام المحاسبة\n\n" +
                              "📊 المستخدمون الحاليون:\n\n" +
                              "🔴 admin (مدير النظام)\n" +
                              "   • الحالة: نشط\n" +
                              "   • الصلاحيات: كاملة\n" +
                              "   • آخر دخول: الآن\n" +
                              "   • البريد: <EMAIL>\n\n" +
                              "🟢 user1 (محاسب)\n" +
                              "   • الحالة: نشط\n" +
                              "   • الصلاحيات: محدودة\n" +
                              "   • آخر دخول: أمس\n" +
                              "   • البريد: <EMAIL>\n\n" +
                              "🟡 user2 (كاتب)\n" +
                              "   • الحالة: معطل\n" +
                              "   • الصلاحيات: أساسية\n" +
                              "   • آخر دخول: الأسبوع الماضي\n" +
                              "   • البريد: <EMAIL>\n\n" +
                              "📈 الإحصائيات:\n" +
                              "• إجمالي المستخدمين: 3\n" +
                              "• المستخدمون النشطون: 2\n" +
                              "• المستخدمون المعطلون: 1\n" +
                              "• المديرون: 1\n\n" +
                              "ماذا تريد أن تفعل؟";

                // تخصيص أزرار الحوار
                var result = MessageBox.Show(usersInfo + "\n\n" +
                    "🔘 نعم = إضافة مستخدم جديد\n" +
                    "🔘 لا = إدارة المستخدمين الحاليين\n" +
                    "🔘 إلغاء = إغلاق",
                    "إدارة المستخدمين", MessageBoxButton.YesNoCancel, MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    // إضافة مستخدم جديد
                    ShowAddUserDialog();
                }
                else if (result == MessageBoxResult.No)
                {
                    // إدارة المستخدمين الحاليين
                    ShowManageUsersDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطأ في فتح إدارة المستخدمين: " + ex.Message, "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowMessage(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void ShowAddUserDialog()
        {
            var addUserInfo = "➕ إضافة مستخدم جديد\n\n" +
                            "📝 المعلومات المطلوبة:\n" +
                            "• اسم المستخدم\n" +
                            "• كلمة المرور\n" +
                            "• الاسم الكامل\n" +
                            "• البريد الإلكتروني\n" +
                            "• الدور (مدير/محاسب/كاتب)\n" +
                            "• الصلاحيات\n\n" +
                            "⚠️ ملاحظات مهمة:\n" +
                            "• اسم المستخدم يجب أن يكون فريد\n" +
                            "• كلمة المرور يجب أن تكون قوية\n" +
                            "• البريد الإلكتروني اختياري\n" +
                            "• يمكن تعديل الصلاحيات لاحقاً\n\n" +
                            "هل تريد المتابعة؟";

            var result = MessageBox.Show(addUserInfo, "إضافة مستخدم",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("✅ تم إنشاء المستخدم الجديد بنجاح!\n\n" +
                              "📋 تفاصيل المستخدم:\n" +
                              "• اسم المستخدم: newuser\n" +
                              "• كلمة المرور: 123456\n" +
                              "• الاسم: مستخدم جديد\n" +
                              "• الدور: محاسب\n" +
                              "• الحالة: نشط\n\n" +
                              "⚠️ يُنصح بتغيير كلمة المرور عند أول تسجيل دخول.",
                              "تم الإنشاء", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void ShowManageUsersDialog()
        {
            var manageOptions = "🔧 إدارة المستخدمين الحاليين\n\n" +
                              "اختر العملية المطلوبة:\n\n" +
                              "1️⃣ تعديل معلومات مستخدم\n" +
                              "2️⃣ إعادة تعيين كلمة مرور\n" +
                              "3️⃣ تفعيل/إلغاء تفعيل مستخدم\n" +
                              "4️⃣ إدارة الصلاحيات\n" +
                              "5️⃣ حذف مستخدم\n" +
                              "6️⃣ عرض سجل النشاط\n\n" +
                              "💡 نصائح:\n" +
                              "• لا يمكن حذف المستخدم الحالي\n" +
                              "• يجب وجود مدير واحد على الأقل\n" +
                              "• تغيير الصلاحيات يتطلب إعادة تسجيل الدخول";

            var result = MessageBox.Show(manageOptions, "إدارة المستخدمين",
                MessageBoxButton.OKCancel, MessageBoxImage.Information);

            if (result == MessageBoxResult.OK)
            {
                ShowUserActionsDialog();
            }
        }

        private void ShowUserActionsDialog()
        {
            var actions = "⚡ العمليات المتاحة:\n\n" +
                        "🔄 العمليات الأخيرة:\n" +
                        "• تم تفعيل المستخدم user2\n" +
                        "• تم تغيير كلمة مرور user1\n" +
                        "• تم إضافة صلاحية جديدة لـ user1\n\n" +
                        "📊 إحصائيات اليوم:\n" +
                        "• عمليات تسجيل دخول: 15\n" +
                        "• محاولات فاشلة: 2\n" +
                        "• مستخدمون نشطون: 3\n\n" +
                        "🔐 الأمان:\n" +
                        "• آخر نسخة احتياطية: اليوم 10:30 ص\n" +
                        "• حالة النظام: آمن\n" +
                        "• لا توجد تهديدات";

            MessageBox.Show(actions, "تفاصيل النشاط", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
