# تشغيل نظام المحاسبة المتقدم
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "       تشغيل نظام المحاسبة المتقدم" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# التحقق من وجود الملف التنفيذي
$exePath = "bin\Debug\AccountingSystem.exe"
if (-not (Test-Path $exePath)) {
    Write-Host "❌ الملف التنفيذي غير موجود" -ForegroundColor Red
    Write-Host "يرجى بناء المشروع أولاً" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "✅ تم العثور على الملف التنفيذي" -ForegroundColor Green
Write-Host "🚀 تشغيل النظام..." -ForegroundColor Yellow
Write-Host ""

# إنهاء أي عمليات سابقة
try {
    Get-Process -Name "AccountingSystem" -ErrorAction SilentlyContinue | Stop-Process -Force
    Start-Sleep -Seconds 1
} catch {
    # لا توجد عمليات سابقة
}

# تشغيل البرنامج
try {
    Start-Process -FilePath $exePath -WorkingDirectory "bin\Debug"
    Write-Host "✅ تم تشغيل النظام بنجاح!" -ForegroundColor Green
    Write-Host ""
    Write-Host "بيانات تسجيل الدخول:" -ForegroundColor Cyan
    Write-Host "👤 اسم المستخدم: admin" -ForegroundColor White
    Write-Host "🔒 كلمة المرور: admin123" -ForegroundColor White
    Write-Host ""
    Write-Host "إذا ظهرت رسالة خطأ Material Design، تجاهلها - البرنامج سيعمل بشكل طبيعي" -ForegroundColor Yellow
} catch {
    Write-Host "❌ فشل في تشغيل النظام" -ForegroundColor Red
    Write-Host "الخطأ: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "اضغط Enter للخروج"
